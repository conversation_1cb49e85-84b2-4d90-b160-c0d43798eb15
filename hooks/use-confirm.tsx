"use client";
import React from "react";
import { useState } from "react";
import {Button,type ButtonProps } from '@/components/ui/button';
import { ResponsiveModal } from "@/components/responsive-modal";
import { Card,CardContent,CardTitle,CardHeader, CardDescription } from "@/components/ui/card";
import { Trash } from "lucide-react";


export const useConfirm=(
    title:string,
    message:string,
    variant:ButtonProps["variant"] ="ghost"
):[()=>JSX.Element,()=>Promise<unknown >]=>{
const [promise,setPromise] =useState<{resolve:(value:boolean)=>void}|null>(null);

const confirm=()=>{
   return   new Promise ((resolve)=>{
        setPromise({resolve})
    });
};

const handleClose =()=>{
    setPromise(null);
};

const handleConfirm=()=>{
    promise ?.resolve(true);
    handleClose();
}

const handleCancle=()=>{
    promise ?.resolve(false);
    handleClose();

}
const ConfirmationDialog=()=>(
    <ResponsiveModal open={promise !== null} onOpenChange={handleClose}>
        <Card className="w-full h-full border-none shadow-none bg-gradient-to-br from-white via-gray-50 to-gray-100 rounded-xl">
            <CardContent className="pt-8">
                <CardHeader className="p-0 flex flex-col items-center">
                    <div className="bg-red-100 rounded-full p-3 mb-4">
                        <Trash className="w-8 h-8 text-red-500" />
                    </div>
                    <CardTitle className="text-center text-2xl font-bold text-gray-800">{title}</CardTitle>
                    <CardDescription className="text-center text-gray-500">{message}</CardDescription>
                </CardHeader>
                <div className="pt-6 w-full flex flex-col lg:flex-row gap-2 items-center justify-end">
                    <Button
                        onClick={handleCancle}
                        variant="outline"
                        size="sm"
                        className="w-full lg:w-auto border-gray-300 hover:bg-gray-100 transition"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirm}
                        variant={variant}
                        size="sm"
                        className="w-full lg:w-auto bg-red-500 hover:bg-red-600 text-white flex items-center gap-2 transition"
                    >
                        <Trash className="w-4 h-4" />
                        Confirm
                    </Button>
                </div>
            </CardContent>
        </Card>
    </ResponsiveModal>
   
);

return [ConfirmationDialog,confirm]



}