import axios from "axios";
import {
    CreateCustomerRequest,
    FetchCustomerRequest,
    UpdateCustomerRequest,
    CreateCustomerResponse,
    UpdateCustomerResponse,
    RetrieveCustomerResponse,
    DeleteCustomerResponse
} from "../types/customers";
import IziA<PERSON> from "@/lib/endpoints";

export class Customer {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : null;

    public static async getAllCustomers(request: FetchCustomerRequest) {
        try {
            const response = await axios.get(IziApi.customers, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async create(form: CreateCustomerRequest): Promise<CreateCustomerResponse> {
        try {
            const response = await axios.post(IziApi.customers, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create customer');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleCustomer(id: string): Promise<RetrieveCustomerResponse> {
        try {
            const response = await axios.get(`${IziApi.customers}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to retrieve customer');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async update(form: UpdateCustomerRequest, id: string): Promise<UpdateCustomerResponse> {
        try {
            const response = await axios.put(`${IziApi.customers}/${id}`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to update customer');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async delete(id: string): Promise<DeleteCustomerResponse> {
        try {
            const response = await axios.delete(`${IziApi.customers}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 204) {
                return response.data || {};
            }
            throw new Error('Failed to delete customer');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
