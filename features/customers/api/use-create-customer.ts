import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateCustomerRequest } from "../types/customers";
import { Customer } from "../server/Customer";

export const useCreateCustomer = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateCustomerRequest) => {

            const response = await Customer.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["customers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create customer";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
