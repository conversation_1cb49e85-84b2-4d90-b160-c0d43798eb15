import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Customer } from "../server/Customer";

export const useDeleteCustomer = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Customer.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Customer deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["customers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete customer";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
