import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateCustomerRequest } from "../types/customers";
import { Customer } from "../server/Customer";

export const useEditCustomer = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateCustomerRequest) => {
            const response = await Customer.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["customers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit customer";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};