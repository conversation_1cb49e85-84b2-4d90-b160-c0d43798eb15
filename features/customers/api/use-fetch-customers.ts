import { useQuery } from "@tanstack/react-query";
import { Customer } from "../server/Customer";
import { FetchCustomerRequest } from "../types/customers";

export const useFetchCustomers = (filters?: FetchCustomerRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["customers", _filters],
    queryFn: () => Customer.getAllCustomers({
        query: _filters.query,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
