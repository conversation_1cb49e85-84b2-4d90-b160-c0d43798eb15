
"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DottedSeparator } from "@/components/dotted-separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
    Form, 
    FormField, 
    FormItem, 
    FormLabel, 
    FormControl, 
    FormMessage 
} from "@/components/ui/form";

import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { createCustomersSchema } from "../schemas";
import { useCustomerFormModal } from "../hooks/use-customer-form-modal";
import { useCreateCustomer } from "../api/use-create-customer";
import { useRetrieveCustomer } from "../api/use-retrieve-customer";
import { useEditCustomer } from "../api/use-edit-customer";
import { useEffect } from "react";


interface CustomerFormProps {
    onCancel?: () => void;
    defaultValues?: z.infer<typeof createCustomersSchema>;
}


export const CustomerForm = ({ onCancel }: CustomerFormProps) => {
    const createCustomer = useCreateCustomer();
    const { close, id } = useCustomerFormModal();
    const { data: customer } = useRetrieveCustomer(id);
    const editCustomer =useEditCustomer(id);
   
    
   const organization_id = localStorage.getItem('current_organization') ?? undefined;

   
    const form = useForm<z.infer<typeof createCustomersSchema>>({
        resolver: zodResolver(createCustomersSchema),
        defaultValues: {
            name: "",
            email: "",
            phone: "",
            bus_name :"",   
            bus_address :"", 
            bus_phone :"", 
            bus_tin  :"",  
            bus_vrn  :"",
            organization_id: organization_id,
        }
    });


    useEffect(() => {
        if (id && customer?.data) {
            const customerData = customer.data;
            form.setValue("name", customerData.name || ""); 
            form.setValue("email", customerData.email || ""); 
            form.setValue("phone", customerData.phone || ""); 
            form.setValue("bus_address", customerData.bus_address ?? ""); 
            form.setValue("bus_name", customerData.bus_name ?? ""); 
            form.setValue("bus_phone", customerData.bus_phone ?? ""); 
            form.setValue("bus_tin", customerData.bus_tin ?? ""); 
            form.setValue("bus_vrn", customerData.bus_vrn ?? ""); 
        }
    }, [customer?.data, id, form]);
    

    const onSubmit = async (values: z.infer<typeof createCustomersSchema>) => {
        //console.log("Values before mutation:", values);
        if (!!id) {
            editCustomer.mutate(
                values,
                {
                    onSuccess: () => {
                        toast.success("Customer updated successfully");
                        form.reset();
                        close();
                    },
                    onError: (error) => {
                        console.error("Error:", error);
                        toast.error(
                            typeof error === "object" && error && "message" in error
                                ? String((error as { message?: unknown }).message)
                                : "An error occurred"
                        );
                    },
                }
            );
            return;
        }
        
        createCustomer.mutate(
            values,
            {
                onSuccess: () => {
                    toast.success("Customer created successfully")
                    form.reset();
                    close();
                },
                onError: (error) => {
                    toast.error(
                        typeof error === "object" && error && "message" in error
                            ? String((error as { message?: unknown }).message)
                            : "An error occurred"
                    )
                }
            }
        );
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    {id ? "Edit Customer" : "Create Customer"}
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
            
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} >
                        <div className="flex flex-col gap-y-4">
                        
                            <FormField 
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter customer name"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Phone</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter phone number"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            
                            <FormField 
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter email" 
                                                type="email"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            
                    
                         
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createCustomer.isPending}
                            >
                                Cancel
                            </Button>
                            <Button disabled={createCustomer.isPending || editCustomer.isPending}>
                                {id ? "Update Customer" : "Create Customer"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
};