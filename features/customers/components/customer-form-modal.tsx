"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useCustomerFormModal } from "../hooks/use-customer-form-modal";
import { CustomerForm } from "./customer-form";


export const CustomerFormModal = () => {
    const { isOpen, setIsOpen, close } = useCustomerFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <CustomerForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}