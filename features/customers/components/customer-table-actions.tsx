import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreHorizontal, Trash } from "lucide-react";
import { Customers } from "../types/customers";
import { useCustomerFormModal } from "../hooks/use-customer-form-modal";
import { useDeleteCustomer } from "../api/use-delete-customer";
import { useConfirm } from "@/hooks/use-confirm";

type Props = {
  customer: Customers;
  organization_id: string;
};

export const CustomerTableActions = ({ customer }: Props) => {
  const editCustomer = useCustomerFormModal();

  const { mutate, isPending } = useDeleteCustomer();

  const [DeletingDialog, confirmDelete] = useConfirm(
    "Delete customer",
    "This action cannot be undone",
    "ghost"
  );

  const handleDelete = async () => {
    const ok = await confirmDelete();
    if (!ok) return;
    mutate(customer.id.toString(), {
      onSuccess: () => {
        console.log("Customer deleted successfully");
      },
      onError: () => {
        console.error("Error deleting customer:", customer.id);
      },
    });
  };

  return (
    <div className="flex items-center space-x-2"> 

    <DeletingDialog/>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={() => editCustomer.edit(customer.id.toString())}
            >
              <Edit /> Edit
            </DropdownMenuItem>

            <DropdownMenuItem
              className="text-red-600 flex"
              disabled={isPending}
              onClick={handleDelete}
            >
              <Trash color="red" /> Delete
              {isPending && <Loader2 className="animate-spin ml-2" />}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};