import {z} from "zod";
import { createCustomersSchema, fetchCustomersSchema } from "../schemas";
export interface Customers {
    id:number;            
    name:string;         
    address :string;      
    phone :string;        
    email :string;         
    deleted_at:string;   
    created_at :string;   
    updated_at :string ;  
    bus_name  :string ;   
    bus_address :string;  
    bus_phone :string;   
    bus_tin  :string ;  
    bus_vrn  :string ;  
    organization_id:string;
}

export type FetchCustomerRequest=z.infer<typeof fetchCustomersSchema>;

export interface FetchCustomerResponse{
    data:Customers[];
    total:number;
}

export interface RetrieveCustomerResponse {
  data: Customers;
}

// CREATE CUSTOMERS
export type CreateCustomerRequest = z.infer<typeof createCustomersSchema>;

//update
export interface CreateCustomerResponse {
  data: Customers;
}

// UPDATE CUSTOMERS
export type UpdateCustomerRequest = z.infer<typeof createCustomersSchema>;

export type UpdateCustomerResponse = CreateCustomerResponse

// DELETE CUSTOMERS
export type DeleteCustomerResponse = {
  data?: Customers;
}