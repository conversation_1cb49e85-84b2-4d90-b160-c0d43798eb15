"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { TrendingUp, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface SalesDisplayProps {
  todaySales: number;
  totalSales?: number;
  currency?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export const SalesDisplay = ({ 
  todaySales, 
  totalSales, 
  currency = "TSH", 
  trend 
}: SalesDisplayProps) => {
  const formatCurrency = (amount: number) => {
    return `${currency} ${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}/=`;
  };

  return (
    <Card className="shadow-sm hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-green-50 via-green-25 to-emerald-50 border border-green-100 hover:border-green-200 backdrop-blur-sm hover:scale-[1.02] hover:-translate-y-1">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          Today's Sales
        </CardTitle>
        <div className="p-2 rounded-lg bg-white/50">
          <TrendingUp className="h-5 w-5 text-green-600" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div>
            <div className="text-3xl font-bold text-green-700 mb-2">
              {formatCurrency(todaySales)}
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'short',
                  day: 'numeric'
                })}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
