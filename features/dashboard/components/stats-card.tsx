"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string | ReactNode;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  valueClassName?: string;
  animate?: boolean;
}

export const StatsCard = ({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  valueClassName,
  animate = true,
}: StatsCardProps) => {
  return (
    <Card className={cn(
      "shadow-sm hover:shadow-lg transition-all duration-300 backdrop-blur-sm",
      animate && "hover:scale-[1.02] hover:-translate-y-1",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="p-2 rounded-lg bg-white/50">
          <Icon className="h-5 w-5 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className={cn(
          "text-2xl font-bold mb-2",
          valueClassName
        )}>
          {value}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground mb-3">
            {description}
          </p>
        )}
        {trend && (
          <div className="flex items-center text-xs">
            <div className={cn(
              "flex items-center px-2 py-1 rounded-full text-xs font-medium",
              trend.isPositive
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            )}>
              <span>
                {trend.isPositive ? "↗" : "↘"} {trend.value}%
              </span>
            </div>
            <span className="text-muted-foreground ml-2">vs last month</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
