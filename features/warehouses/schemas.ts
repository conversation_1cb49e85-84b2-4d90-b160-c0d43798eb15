import { z } from "zod";

export const fetchWarehousesSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    sort_by: z.string().optional(),
    sort_direction: z.string().optional(),
});


export const createWarehouseSchema = z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    organization_id: z.string().min(1,"Organization ID is required"),    
});