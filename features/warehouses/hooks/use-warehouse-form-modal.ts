import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';

export const useWareHouseFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "warehouse-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState(
    "warehouse-form-id", 
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}