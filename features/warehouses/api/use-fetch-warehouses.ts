import { useQuery } from "@tanstack/react-query";
import { FetchWarehousesRequest } from "../types/warehouses";
import { Warehouse } from "../server/Warehouse";

export const useFetchWarehouses = (filters?: FetchWarehousesRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["warehouses", _filters],
    queryFn: () => Warehouse.getAllWarehouses({
        query: _filters.query,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
