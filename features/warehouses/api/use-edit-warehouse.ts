import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateWarehouseRequest } from "../types/warehouses";
import { Warehouse } from "../server/Warehouse";

export const useEditWarehouse = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateWarehouseRequest) => {
            const response = await Warehouse.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["warehouses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit warehouse";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};