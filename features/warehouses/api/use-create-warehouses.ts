import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateWarehouseRequest } from "../types/warehouses";
import { Warehouse } from "../server/Warehouse";

export const useCreateWarehouse= () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateWarehouseRequest) => {

            const response = await Warehouse.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["warehouses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create warehouse";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
