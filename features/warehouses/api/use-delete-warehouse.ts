import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Warehouse } from "../server/Warehouse";

export const useDeleteWarehouse = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Warehouse.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Warehouse deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["warehouses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete warehouse";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
