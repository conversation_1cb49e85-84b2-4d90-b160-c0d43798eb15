import { z } from "zod";
import { createWarehouseSchema, fetchWarehousesSchema } from "../schemas";

// Warehouse TypeScript interface
export interface Warehouse {
    id: string;

    name: string;
    description?: string | null;
    featured_image?: string | null;

    deleted_at?: string | null;
    created_at?: string | null;
    updated_at?: string | null;

    organization_id?: string | null;
}

// FETCH WAREHOUSES REQUEST AND RESPONSE
export interface FetchWarehousesResponse {
    data: Warehouse[];
    total: number;
}

export type FetchWarehousesRequest = z.infer<typeof fetchWarehousesSchema>;

// RETRIEVE WAREHOUSE REQUEST AND RESPONSE
export interface RetrieveWarehouseResponse {
    data: Warehouse;
}

// CREATE WAREHOUSE REQUEST AND RESPONSE
export type CreateWarehouseRequest = z.infer<typeof createWarehouseSchema>;

export interface CreateWarehouseResponse {
    data: Warehouse;
}

// UPDATE WAREHOUSE REQUEST AND RESPONSE
export type UpdateWarehouseRequest = z.infer<typeof createWarehouseSchema>;

export interface UpdateWarehouseResponse {
    data: Warehouse;
}

// DELETE WAREHOUSE RESPONSE
export type DeleteWarehouseResponse = {
    data?: Warehouse;
};