"use client";

import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowDown, ArrowUp, ArrowUpDown, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Warehouse } from "./types/warehouses";
import { WarehouseTableActions } from "./components/warehouse-table-actions";

export const warehouseColumns = (organization_id: string): ColumnDef<Warehouse>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Name
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
  },
  {
    accessorKey: "description",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Description
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Created
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
    cell: ({ row }) => formatDate(row.original.created_at ?? ""),
  },
    {
    accessorKey: "updated_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Updated</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at ?? "");
    },
  },

  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const warehouse = row.original;

      return <WarehouseTableActions warehouse={warehouse} organization_id={organization_id}/>
    },
  },
];
