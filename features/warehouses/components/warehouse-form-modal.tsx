"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { WarehouseForm } from "./warehouse-form";
import { useWareHouseFormModal } from "../hooks/use-warehouse-form-modal";

export const WarehouseFormModal = () => {
    const { isOpen, setIsOpen, close } = useWareHouseFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <WarehouseForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}