import { DataTable } from "@/components/data-table";
import React, { useCallback } from "react";
import { Row, SortingState } from "@tanstack/react-table";
import { FetchWarehousesRequest, FetchWarehousesResponse, Warehouse } from "../types/warehouses";
import { useFetchWarehouses } from "../api/use-fetch-warehouses";
import { warehouseColumns } from "../columns";

interface WarehouseTableProps {
  onRowSelectedChanged?: (rows: Row<Warehouse>[]) => void;
  onDataChange?: (data?: FetchWarehousesResponse | undefined) => void;
  searchQuery?: string;
  filters?: FetchWarehousesRequest;
  onSearchChange?: (value: string) => void;
  organization_id: string;
}

export const WarehouseTable = (props: WarehouseTableProps = {} as WarehouseTableProps) => {
  const [request, setRequest] = React.useState<FetchWarehousesRequest>({
    organization_id: props.organization_id,
    query: props.searchQuery,
    ...props.filters,
  });
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<Warehouse>[]>([]);

  const { data: warehouses, isLoading } = useFetchWarehouses(request);

  // Update request when filters or search query change
  React.useEffect(() => {
    setRequest(prev => {
      const newRequest = { ...prev };
      
      // Add filters if provided
      if (props?.filters) {
        Object.assign(newRequest, props.filters);
      }
      
      // Add search query if provided
      if (props?.searchQuery) {
        newRequest.query = props.searchQuery;
      } else if ('query' in newRequest) {
        // Remove the query property if it exists and searchQuery is empty
        delete newRequest.query;
      }
      
      return newRequest;
    });
  }, [props?.filters, props?.searchQuery]);

  // Handle sorting
  const handleSortingChange = useCallback((sortState: SortingState) => {
    setSorting(sortState);
    
    if (sortState.length > 0) {
      const { id, desc } = sortState[0];
      // Apply sorting to the request
      setRequest(prev => {
        const updatedRequest = {
          ...prev,
          sort_by: id,
          sort_direction: desc ? 'desc' as const : 'asc' as const,
        };
        return updatedRequest;
      });
    } else {
      setRequest(prev => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { sort_by, sort_direction, ...rest } = prev;
        return rest;
      });
    }
  }, []);

  // Effect to notify parent about row selection changes
  const { onRowSelectedChanged } = props || {};

  React.useEffect(() => {
    if (onRowSelectedChanged) {
      onRowSelectedChanged(rowSelection);
    }
  }, [rowSelection, onRowSelectedChanged]);

  const { onDataChange } = props || {};

  React.useEffect(() => {
    if (onDataChange && warehouses){
      onDataChange(warehouses);
    }
  }, [warehouses, onDataChange]);

  const { onDataChange: handleDataChange } = props || {};

  React.useEffect(() => {
    if (handleDataChange && warehouses) {
      handleDataChange(warehouses);
    }
  }, [warehouses, handleDataChange]);

  return (
    <DataTable
      columns={warehouseColumns(props.organization_id)}
      data={warehouses?.data || []}
      manualPagination={true}
      manualSorting={true}
      total={warehouses?.total || 0}
      sorting={sorting}
      onSortingChange={handleSortingChange}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
            per_page: pg.pageSize.toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};