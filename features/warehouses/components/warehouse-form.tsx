"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DottedSeparator } from "@/components/dotted-separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
    Form, 
    FormField, 
    FormItem, 
    FormLabel, 
    FormControl, 
    FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { useEffect } from "react";
import { createWarehouseSchema } from "../schemas";
import { useWareHouseFormModal } from "../hooks/use-warehouse-form-modal";
import { useEditWarehouse } from "../api/use-edit-warehouse";
import { useRetrieveWarehouse } from "../api/use-retrieve-warehouse";
import { useCreateWarehouse } from "../api/use-create-warehouses";

interface WarehouseFormProps {
    onCancel?: () => void;
    defaultValues?: z.infer<typeof createWarehouseSchema>;
}


export const WarehouseForm = ({ onCancel }: WarehouseFormProps) => {
    const organizationId = localStorage.getItem("current_organization") ?? undefined;
    const createWarehouse= useCreateWarehouse();
    const { close, id } = useWareHouseFormModal();
    const { data: warehouse } = useRetrieveWarehouse(id);
    const editWarehouse= useEditWarehouse(id);
    

    const form = useForm<z.infer<typeof createWarehouseSchema>>({
        resolver: zodResolver(createWarehouseSchema),
        defaultValues: {
            name: "",
            description: "", 
            organization_id:organizationId || "",}
    });

    console.log(warehouse?.data)

    useEffect(() => {
        if (id && warehouse?.data) {
            const warehouseData = warehouse.data;

            form.setValue("name", warehouseData.name);
            form.setValue("description", warehouseData.description ?? "");

        }
    }, [warehouse?.data, id, form]);

    const onSubmit = async (values: z.infer<typeof createWarehouseSchema>) => {
        if (id) {
            editWarehouse.mutate(
                values , 
                {
                    onSuccess: () => {
                        toast.success("Warehouse updated successfully");
                        form.reset();
                        close();
                    },
                }
            );
            return;
        }
        
        createWarehouse.mutate(
            values,
            {
                onSuccess: () => {
                    toast.success("Warehouse created successfully")
                    form.reset();
                    close();
                },
            }
        );
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    {id ? "Edit Store Warehouse" : "Create Store Warehouse"}
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} >
                        <div className="flex flex-col gap-y-4">
                            <FormField 
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Full Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter name"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter desc" 
                                                type="text"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createWarehouse.isPending}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createWarehouse.isPending || editWarehouse.isPending}
                            >
                                {id ? "Update Warehouse" : "Create Warehouse"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
};