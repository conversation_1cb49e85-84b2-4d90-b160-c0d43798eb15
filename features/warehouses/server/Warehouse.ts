import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateWarehouseRequest, CreateWarehouseResponse, DeleteWarehouseResponse, FetchWarehousesRequest, RetrieveWarehouseResponse, UpdateWarehouseRequest, UpdateWarehouseResponse } from "../types/warehouses";

export class Warehouse {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllWarehouses(request: FetchWarehousesRequest) {
        try {
            const response = await axios.get(IziApi.warehouses, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }


    public static async create(form: CreateWarehouseRequest): Promise<CreateWarehouseResponse> {
                try {
                    const response = await axios.post(IziApi.warehouses, form, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (response.status === 200 || response.status === 201) {
                        return response.data;
                    }
                    throw new Error('Failed to create warehouse');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async getSingleWarehouse(id: string): Promise<RetrieveWarehouseResponse> {
                try {
                    const response = await axios.get(`${IziApi.warehouses}/${id}`, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                        },
                    });
    
                    if (response.status === 200) {
                        return response.data;
                    }
                    throw new Error('Failed to retrieve warehouse');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async update(form: UpdateWarehouseRequest, id: string): Promise<UpdateWarehouseResponse> {
                try {
                    const response = await axios.put(`${IziApi.warehouses}/${id}`, form, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (response.status === 200) {
                        return response.data;
                    }
                    throw new Error('Failed to update warehouse');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async delete(id: string): Promise<DeleteWarehouseResponse> {
                try {
                    const response = await axios.delete(`${IziApi.warehouses}/${id}`, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                        },
                    });
        
                    if (response.status === 200 || response.status === 204) {
                        return response.data || {};
                    }
                    throw new Error('Failed to delete warehouse');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
}
