import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateInventoryCategoryRequest } from "../types/inventoryCategories";
import { InventoryCategory } from "../server/InventoryCategory";

export const useCreateInventoryCategory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateInventoryCategoryRequest) => {

            const response = await InventoryCategory.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["inventory-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create inventory categories";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
