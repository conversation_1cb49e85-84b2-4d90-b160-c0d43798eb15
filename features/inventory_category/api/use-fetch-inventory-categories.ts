import { useQuery } from "@tanstack/react-query";
import { FetchInventoryCategoryRequest } from "../types/inventoryCategories";
import { InventoryCategory } from "../server/InventoryCategory";

export const useFetchInventoryCategories = (filters?: FetchInventoryCategoryRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["inventory-categories", _filters],
    queryFn: () => InventoryCategory.getAllInventoryCategories({
        search: _filters.search,
        query: _filters.query,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
