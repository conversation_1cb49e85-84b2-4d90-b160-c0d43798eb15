import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateInventoryCategoryRequest } from "../types/inventoryCategories";
import { InventoryCategory } from "../server/InventoryCategory";

export const useEditInventoryCategory = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateInventoryCategoryRequest) => {
            const response = await InventoryCategory.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["inventory-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit Inventory category";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};