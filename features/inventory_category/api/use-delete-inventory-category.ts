import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { InventoryCategory } from "../server/InventoryCategory";

export const useDeleteInventoryCategory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await InventoryCategory.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("inventory Category deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["inventory-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete inventory category";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
