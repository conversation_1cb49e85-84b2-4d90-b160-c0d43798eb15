import { z } from "zod";
import { createInventoryCategorySchema } from "../schemas";

export interface InventoryCategory {
    id: number;
    name: string;
    description: string;   

    parent_id?: string;
    organization_id: string;

    created_at: string;
    updated_at: string;
    deleted_at: string;
}

//Fetch Roles
export type FetchInventoryCategoryRequest = {
    query?: string;
    with_trashed?: boolean;
    page?: string;
    organization_id?: string;
    search?: string;
    parent_id?: string;
}

export type FetchInventoryCategoryResponse = {
    data: InventoryCategory[];
    total: number;
}

// Create Organization
export type CreateInventoryCategoryRequest = z.infer<typeof createInventoryCategorySchema>;

export interface CreateInventoryCategoryResponse {
  data: InventoryCategory;
}

// Update Organization
export type UpdateInventoryCategoryRequest = z.infer<typeof createInventoryCategorySchema>;

// use the same response as create category
export type UpdateInventoryCategoryResponse = CreateInventoryCategoryResponse;

// Retrieve Organization
export interface RetrieveInventoryCategoryResponse {
    data: InventoryCategory;
  }
  

// Delete category
export interface DeleteInventoryCategoryResponse {
    data?: InventoryCategory;
  }