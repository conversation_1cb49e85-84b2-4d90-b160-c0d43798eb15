import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateInventoryCategoryRequest, CreateInventoryCategoryResponse, DeleteInventoryCategoryResponse, FetchInventoryCategoryRequest, RetrieveInventoryCategoryResponse, UpdateInventoryCategoryRequest, UpdateInventoryCategoryResponse } from "../types/inventoryCategories";

export class InventoryCategory {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllInventoryCategories(request: FetchInventoryCategoryRequest) {
        try {
            const response = await axios.get(IziApi.inventoryCategories, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                console.log(response);
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async create(form: CreateInventoryCategoryRequest): Promise<CreateInventoryCategoryResponse> {
        try {
            const response = await axios.post(IziApi.inventoryCategories, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create inventory category');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleInventoryCategory(id: string): Promise<RetrieveInventoryCategoryResponse> {
        try {
            const response = await axios.get(`${IziApi.inventoryCategories}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response;
            }
            throw new Error('Failed to retrieve inventory category');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async update(form: UpdateInventoryCategoryRequest, id: string): Promise<UpdateInventoryCategoryResponse> {
        try {
            const response = await axios.put(`${IziApi.inventoryCategories}/${id}`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to update inventory category');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async delete(id: string): Promise<DeleteInventoryCategoryResponse> {
        try {
            const response = await axios.delete(`${IziApi.inventoryCategories}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 204) {
                return response.data || {};
            }
            throw new Error('Failed to delete inventory category');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
