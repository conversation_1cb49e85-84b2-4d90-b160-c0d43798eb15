import React from "react";
import { Row } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import { FetchInventoryCategoryRequest, FetchInventoryCategoryResponse, InventoryCategory } from "../types/inventoryCategories";
import { useFetchInventoryCategories } from "../api/use-fetch-inventory-categories";
import { inventoryCategoryColumns } from "../column";
interface InventoryCategoryTableProps {
  onRowSelectedChanged?: (rows: Row<InventoryCategory>[]) => void,
  onDataChange?: (data?: FetchInventoryCategoryResponse | undefined) => void;
  filters?: FetchInventoryCategoryRequest;
}

export const InventoryCategoryTable = (props?: InventoryCategoryTableProps) => {
  const [request, setRequet] = React.useState<FetchInventoryCategoryRequest>(props?.filters ?? {});

  const { data: categories , isLoading} = useFetchInventoryCategories(request);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<InventoryCategory>[]>([]);


  React.useEffect(() => {
    props?.onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, props]);

  React.useEffect(() => {
    props?.onDataChange?.(categories?.data);
  }, [categories?.data, props]);

  return (
    <DataTable
      columns={inventoryCategoryColumns}
      data={categories?.data ? categories.data : []}
      manualPagination={true}
      total={categories?.length}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequet((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  )
}