
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreHori<PERSON>tal, Trash } from "lucide-react";
import React from "react";
import { InventoryCategory } from "../types/inventoryCategories";
import { useInventoryCategoryFormModal } from "../hooks/use-inventory-category-form-modal";
import { useDeleteInventoryCategory } from "../api/use-delete-inventory-category";
import { useConfirm } from "@/hooks/use-confirm";

type Props = {
  category: InventoryCategory;
};

export const InventoryCategoryTableActions = ({ category }: Props) => {

  
const { edit } = useInventoryCategoryFormModal();

const { mutate, isPending } = useDeleteInventoryCategory();
  
    const [DeletingDialog, confirmDelete] = useConfirm(
      "Delete Inventory Category",
      "This action cannot be undone",
      "ghost"
    );
  
    const handleDelete = async () => {
      const ok = await confirmDelete();
      if (!ok) return;
      mutate(category.id.toString(), {
        onSuccess: () => {
          console.log("inventory category deleted successfully");
        },
        onError: () => {
          console.error("Error deleting inventory category:", category.id);
        },
      });
    };

  return (
    <>
    <DeletingDialog />
   
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem className="flex" onClick={() => router.push(`/products/${category.id}?type=category`)}>
            <Eye /> View
          </DropdownMenuItem> */}
          <DropdownMenuItem
             onClick={() => edit(category.id.toString())}
            >
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          ><Trash color="red" /> Delete 
           {isPending && <Loader2 className="spin"/> }
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
     </>
  );
};