
"use client";

import { ResponsiveModal } from "@/components/responsive-modal";
import { useInventoryCategoryFormModal } from "../hooks/use-inventory-category-form-modal";
import { InventoryCategoryForm } from "./inventory-category-form";

export const InventoryCategoryFormModal = () => {
    const { isOpen, setIsOpen, close } = useInventoryCategoryFormModal();
    
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}> 
            <InventoryCategoryForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}