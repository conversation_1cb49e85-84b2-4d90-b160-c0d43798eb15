"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DottedSeparator } from "@/components/dotted-separator";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useEffect } from "react";
import { toast } from "sonner";
import { useInventoryCategoryFormModal } from "../hooks/use-inventory-category-form-modal";
import { createInventoryCategorySchema } from "../schemas";
import { useCreateInventoryCategory } from "../api/use-create-inventory-category";
import { useRetrieveInventoryCategory } from "../api/use-retrieve-inventory-category";
import { useEditInventoryCategory } from "../api/use-edit-inventory-category";
import { useFetchInventoryCategories } from "../api/use-fetch-inventory-categories";

interface CategoryFormProps {
  onCancel?: () => void;
  defaultValues?: z.infer<typeof createInventoryCategorySchema>;
}

export const InventoryCategoryForm = ({ onCancel }: CategoryFormProps) => {
  const createInventoryCategory = useCreateInventoryCategory();
  const { close, id } = useInventoryCategoryFormModal();

  const { data: category } = useRetrieveInventoryCategory(id);
  const { data: categories } = useFetchInventoryCategories({
    organization_id: localStorage.getItem("current_organization") ?? "",
  });
  console.log(category);
  const editInventoryCategory = useEditInventoryCategory(id);

  // const inputRef = useRef<HTMLInputElement>(null);

  const form = useForm<z.infer<typeof createInventoryCategorySchema>>({
    resolver: zodResolver(createInventoryCategorySchema),
    defaultValues: {
      name: "",
      description: "",
    //   featured_image: "",
      organization_id: localStorage.getItem("current_organization") ?? "",
      parent_id: "",
    },
  });

  useEffect(() => {
    if (!id) return;
    if (id && category) {
      const c: any = (category as any)?.data?.data ?? (category as any)?.data ?? category;
      if (!c) return;
      form.reset({
        name: c.name ?? "",
        description: c.description ?? "",
        organization_id: String(c.organization_id ?? localStorage.getItem("current_organization") ?? ""),
        parent_id: c.parent_id != null ? String(c.parent_id) : "",
      });
    }
  }, [category, id, form]);

  const onSubmit = (values: z.infer<typeof createInventoryCategorySchema>) => {
    
    if (!!id) {
      editInventoryCategory.mutate(values , {
            onSuccess: () => {
                toast.success("Category updated successfully");
                form.reset();
                close();
            },
            onError: (error) => {
                const errorMessage = typeof error === "object" && error !== null && "message" in error
                  ? (error as { message: string }).message
                  : "An error occurred";
                toast.error(errorMessage);
            },
        });
        return;
    }
    createInventoryCategory.mutate(values , {
        onSuccess: () => {
            toast.success("Category created successfully");
            form.reset();
            close();
            // Redirect to the organization page
        },
        onError: (error) => {
            const errorMessage = typeof error === "object" && error !== null && "message" in error
              ? (error as { message: string }).message
              : "An error occurred";
            toast.error(errorMessage);
        },
    }); 
  };


  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold">
          {id ? "Edit Category" : "Create Category"}
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <DottedSeparator />
      </div>

      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-y-4">
                <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                        <Input {...field} placeholder="Enter category name" />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
                <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                        <Input {...field} placeholder="Enter description" />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                    )}
                />
            </div>

            {/* Parent Category */}
            <FormField
              control={form.control}
              name="parent_id"
              render={({ field }) => (
                <FormItem className="mt-4">
                  <FormLabel>Parent Category</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={(val) => field.onChange(val === "none" ? "" : val)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select parent category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Categories</SelectLabel>
                        <SelectItem key="none" value="none">None</SelectItem>
                        {categories?.data?.map((c: any) => (
                          <SelectItem key={c.id} value={String(c.id)}>
                            {c.name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DottedSeparator className="py-7" />
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={createInventoryCategory.isPending || editInventoryCategory.isPending}
              >
                Cancel
              </Button>
              <Button disabled={createInventoryCategory.isPending || editInventoryCategory.isPending}>
                {id ? "Save Category" : "Create Category"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}