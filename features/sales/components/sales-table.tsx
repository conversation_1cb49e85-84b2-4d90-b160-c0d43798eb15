import { DataTable } from "@/components/data-table";
import React from "react";
import { Row } from "@tanstack/react-table";
import { FetchSalesRequest, FetchSalesResponse, Sale } from "../types/sales";
import { useFetchSales } from "../api/use-fetch-sales";
import { salesColumns } from "../column";

interface SaleTableProps {
  onRowSelectedChanged?: (rows: Row<Sale>[]) => void,
  onDataChange?: (data?: FetchSalesResponse) => void;
  filters?: FetchSalesRequest;
}

export const SalesTable = (props?: SaleTableProps) => {

  const [request, setRequet] = React.useState<FetchSalesRequest>({
    organization_id: props?.filters?.organization_id ?? "",
    search: props?.filters?.search,
    start_date: props?.filters?.start_date,
    end_date: props?.filters?.end_date,
    agent_id: props?.filters?.agent_id,
    with_trashed: props?.filters?.with_trashed,
    page: props?.filters?.page,
  });

  const { data: sales , isLoading } = useFetchSales(request);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<Sale>[]>([]);

  React.useEffect(() => {
    props?.onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, props]);

  React.useEffect(() => {
    props?.onDataChange?.(sales);
  }, [sales, props]);

  React.useEffect(() => {
    // only set the changed params of the filter, the unchaged leave as is
    setRequet((prev) => ({
      ...prev,
      ...props?.filters,
    }));
  }, [props?.filters]);

  return (
    <DataTable
      columns={salesColumns}
      data={sales?.data ?? []}
      manualPagination={true}
      total={sales?.total}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          console.log({ pg, message: "pagination changed" });
          setPagination(pg);
          setRequet((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
        // setRequet((prev) => ({
        //   ...prev,
        //   page: pagination.pageIndex ? (pagination.pageIndex + 1).toString() : "1",
        // }));
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};