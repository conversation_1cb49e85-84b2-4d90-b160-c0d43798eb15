import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod'; // Added resolver import
import { Button } from '@/components/ui/button';
import { DottedSeparator } from '@/components/dotted-separator';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useParams } from 'next/navigation';
import { createSaleSchema } from '../schemas';
import { ColumnDef } from '@tanstack/react-table';
import { useFetchCustomers } from '@/features/customers/api/use-fetch-customers';
import { Edit3Icon, Trash2Icon } from 'lucide-react';
import { toast } from 'sonner';
import { useStateManager } from '@/hooks/use-context';
import { useFetchInventories } from '@/features/inventories/api/use-fetch-inventories';
import { useCreateSale } from '../api/use-create-sale';
import { InventoryItem } from '@/features/inventories/types/inventories';
import { DataTableWithoutNavigation } from '@/components/data-table-without-navigations';
import { Customers } from '@/features/customers/types/customers';
import { StockItem } from '../types/sales';

// Define Item type alias
type Item = {
  inv_item_id: string;
  conf_unit_id: string;
  quantity: string;
  unit_price: string;
  inv_stock_item_id: string;
  inv_stock_item_in_stock: string;
};

interface SaleFormProps {
  onCancel: () => void;
  defaultValues?: z.infer<typeof createSaleSchema>;
}

const SalesForm = ({ onCancel }: SaleFormProps) => {
  const { user } = useStateManager();
  const organizationId = localStorage.getItem("current_organization") ?? undefined;
  const { data: customers } = useFetchCustomers({ organization_id: organizationId });
  const { data: inventories } = useFetchInventories({ organization_id: organizationId, query: "" });
  const createSale = useCreateSale();

  console.log(inventories)
  // Use zodResolver with useForm
  const form = useForm<z.infer<typeof createSaleSchema>>({
    resolver: zodResolver(createSaleSchema),
    defaultValues: {
      user_id: user?.id ? String(user.id) : undefined,
      organization_id: organizationId,
      date: new Date().toISOString().split('T')[0],
      customer_id: "",
      description: "",
      received_amount: "",
      total_amount: "",
      return_amount: "0",
      sale_type: "cash",
      status:"approved",
       items: [],
    },
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const [newItem, setNewItem] = useState<Item>({
    inv_item_id: "",
    conf_unit_id: "",
    quantity: "",
    unit_price: "",
    inv_stock_item_id: "",
    inv_stock_item_in_stock: "",
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [editingItem, setEditingItem] = useState<Item | null>(null);

  useEffect(() => {
    if (newItem.inv_item_id) {
      const selectedInventory = inventories?.data?.find(inv => inv.id === Number(newItem.inv_item_id));
      if (selectedInventory) {
        setNewItem(prev => ({ ...prev, unit_price: String(selectedInventory.sale_price || "") }));
      }
    }
  }, [newItem.inv_item_id, inventories]);

  useEffect(() => {
    if (isEditing && editingItem?.inv_item_id) {
      const selectedInventory = inventories?.data?.find(inv => inv.id === Number(editingItem.inv_item_id));
      if (selectedInventory) {
        setEditingItem(prev => prev ? { ...prev, unit_price: String(selectedInventory.sale_price || "") } : null);
      }
    }
  }, [editingItem?.inv_item_id, inventories, isEditing]);

  useEffect(() => {
    const totalSum = fields.reduce((sum, field) => {
      const quantity = parseFloat(field.quantity || "0");
      const unitPrice = parseFloat(field.unit_price || "0");
      return sum + quantity * unitPrice;
    }, 0);

    const currentTotal = parseFloat(form.getValues("total_amount") || "0");
    if (parseFloat(totalSum.toFixed(2)) !== currentTotal) {
      form.setValue("total_amount", totalSum.toFixed(2), { shouldDirty: false });
    }
  }, [fields, form]);

  const handleAddItem = () => {
    if (!newItem.inv_item_id || !newItem.conf_unit_id || !newItem.quantity || !newItem.unit_price || !newItem.inv_stock_item_id) {
      console.error("Please fill all required fields");
      return;
    }
    append(newItem);
    setNewItem({
      inv_item_id: "",
      conf_unit_id: "",
      quantity: "",
      unit_price: "",
      inv_stock_item_id: "",
      inv_stock_item_in_stock: "",
    });
  };

  const handleEdit = (index: number) => {
    const item = form.getValues().items[index];
    setEditingItem({ ...item });
    setEditIndex(index);
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (editIndex !== null && editingItem) {
      update(editIndex, editingItem);
      setIsEditing(false);
      setEditingItem(null);
      setEditIndex(null);
    }
  };

  const itemColumns: ColumnDef<{
    id: number;
    itemName: string;
    unitName: string;
    quantity: string;
    unitPrice: string;
    total: string;
  }>[] = [
    { header: "Item", accessorKey: "itemName" },
    { header: "Unit", accessorKey: "unitName" },
    { header: "Quantity", accessorKey: "quantity" },
    { header: "Unit Price", accessorKey: "unitPrice" },
    { header: "Total", accessorKey: "total" },
    {
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button onClick={() => handleEdit(row.original.id)}><Edit3Icon /></Button>
          <Button variant="destructive" onClick={() => remove(row.original.id)}><Trash2Icon /></Button>
        </div>
      ),
    },
  ];

  const itemData = fields.map((field, index) => {
    const selectedInventory = inventories?.data?.find(inv => inv.id === Number(field.inv_item_id));
    const unit = Array.isArray(selectedInventory?.units)
      ? selectedInventory.units.find(u => u.id === Number(field.conf_unit_id))
      : undefined;
    const quantity = parseFloat(field.quantity || "0");
    const unitPrice = parseFloat(field.unit_price || "0");
    const total = (quantity * unitPrice).toFixed(2);
    return {
      id: index,
      itemName: selectedInventory?.name || "Unknown",
      unitName: unit?.name || "Unknown",
      quantity: field.quantity,
      unitPrice: field.unit_price,
      total,
    };
  });

  const onSubmit = (values: z.infer<typeof createSaleSchema>) => {
    console.log("Form values:", values);
    console.log("Items type:", typeof values.items, values.items);
    createSale.mutate(
      {
          ...values,
          items: values.items.map(item => ({
            inv_item_id: item.inv_item_id.toString(),
            conf_unit_id: item.conf_unit_id.toString(),
            quantity: item.quantity.toString(),
            unit_price: item.unit_price.toString(),
            inv_stock_item_id: item.inv_stock_item_id.toString(),
            inv_stock_item_in_stock: item.inv_stock_item_in_stock.toString(),
          })),
      },
      {
        onSuccess: () => {
          toast.success("Sales created successfully");
          form.reset();
          onCancel(); // Changed from close() to onCancel()
        },
        onError: (error) => {
          const errorMessage =
            typeof error === "object" && error !== null && "message" in error
              ? (error as { message: string }).message
              : "An error occurred";
          toast.error(errorMessage);
        },
      }
    );
  };

  const renderItemInputs = (
    item: Item,
    setItem: React.Dispatch<React.SetStateAction<Item>>
  ) => {
    const selectedInventory = item.inv_item_id
      ? inventories?.data?.find(inv => inv.id === Number(item.inv_item_id))
      : null;

    const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const quantity = e.target.value;
      setItem(prev => ({ ...prev, quantity }));
    };

    const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const unit_price = e.target.value;
      setItem(prev => ({ ...prev, unit_price }));
    };

    const total = (parseFloat(item.quantity || "0") * parseFloat(item.unit_price || "0")).toFixed(2);

    return (
      <div className="flex flex-col gap-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div className="md:col-span-2">
            <FormItem>
              <FormLabel>Choose Inventory</FormLabel>
              <Select
                value={item.inv_item_id}
                onValueChange={(value) =>
                  setItem(prev => ({
                    ...prev,
                    inv_item_id: value,
                    conf_unit_id: "",
                    inv_stock_item_id: "",
                  }))
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Inventory" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Inventories</SelectLabel>
                    {inventories?.data?.map((inventory: InventoryItem) => (
                      <SelectItem key={inventory.id} value={inventory.id.toString()}>
                        {inventory.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </FormItem>
          </div>
          <div>
            <FormItem>
              <FormLabel>Unit</FormLabel>
              <Select
                value={item.conf_unit_id}
                onValueChange={(value) => setItem(prev => ({ ...prev, conf_unit_id: value }))}
                disabled={!selectedInventory}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={selectedInventory ? "Select Unit" : "Select Inventory First"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Units</SelectLabel>
                    {Array.isArray(selectedInventory?.units) && selectedInventory.units.map((unit) => (
                      <SelectItem key={unit.id} value={unit.id.toString()}>
                        {unit.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </FormItem>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div>
            <FormItem>
              <FormLabel>Stock Item</FormLabel>
              <Select
                value={item.inv_stock_item_id}
                onValueChange={(value) => {
                  const selectedStockItem = selectedInventory?.stock_items?.find(
                    (si) => si.id === Number(value)
                  );
                  setItem(prev => ({
                    ...prev,
                    inv_stock_item_id: value,
                    inv_stock_item_in_stock: selectedStockItem ? String(selectedStockItem.in_stock) : "",
                  }));
                }}
                disabled={!selectedInventory}
              >
                <SelectTrigger>
                  <SelectValue placeholder={selectedInventory ? "Select Stock Item" : "Select Inventory First"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Stock Items</SelectLabel>
                    {selectedInventory?.stock_items?.map((stockItem : StockItem) => (
                      <SelectItem
                        key={stockItem.id}
                        value={(stockItem.id ?? "").toString()}
                        disabled={(Number(stockItem.in_stock) ?? 0) <= 0}
                      >
                        {`In Stock: ${stockItem.in_stock}`}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </FormItem>
          </div>
          <div>
            <FormItem>
              <FormLabel>Quantity</FormLabel>
              <Input
                value={item.quantity}
                onChange={handleQuantityChange}
                placeholder="Enter Quantity"
                type="number"
              />
            </FormItem>
          </div>
          <div>
            <FormItem>
              <FormLabel>Unit Price</FormLabel>
              <Input
                value={item.unit_price}
                onChange={handleUnitPriceChange}
                placeholder="Enter Unit Price"
                type="number"
              />
            </FormItem>
          </div>
        </div>
        <div className="flex justify-end">
          <p>Total: {total}</p>
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold">Create Sale</CardTitle>
      </CardHeader>
      <div className="px-7">
        <DottedSeparator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <div className="flex flex-col gap-y-4">
                  {isEditing && editingItem
                    ? renderItemInputs(
                        editingItem!,
                        setEditingItem as React.Dispatch<React.SetStateAction<Item>>
                      )
                    : renderItemInputs(newItem, setNewItem)}
                  <div className="flex justify-end gap-2">
                    {isEditing ? (
                      <>
                        <Button type="button" onClick={handleSaveEdit}>
                          Save
                        </Button>
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={() => {
                            setIsEditing(false);
                            setEditingItem(null);
                            setEditIndex(null);
                          }}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <Button type="button" onClick={handleAddItem}>
                        Add Item
                      </Button>
                    )}
                  </div>
                  <div>
                    <DataTableWithoutNavigation columns={itemColumns} data={itemData} />
                    <DottedSeparator className="my-2" />
                    <div className="flex flex-col items-end mt-4 space-y-2">
                      <div className="flex justify-between w-full text-lg font-bold">
                        <span>Total:</span>
                        <span className="ml-2 text-green-600">{form.watch("total_amount")}</span>
                      </div>
                      <div className="flex justify-between w-full text-lg font-bold">
                        <span>Received:</span>
                        <span className="ml-2 text-blue-600">{form.watch("received_amount")}</span>
                      </div>
                      <div className="flex justify-between w-full text-lg font-bold">
                        <span>Return:</span>
                        <span className="ml-2 text-red-600">
                          {(
                            parseFloat(form.watch("total_amount") || "0") -
                            parseFloat(form.watch("received_amount") || "0")
                          ).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:col-span-1">
                <div className="flex flex-col gap-y-4">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date</FormLabel>
                        <FormControl>
                          <Input {...field} type="date" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="customer_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                          required={true}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select Customer" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {customers?.data?.map((customer: Customers) => (
                                <SelectItem key={customer.id} value={customer.id.toString()}>
                                  {customer.name}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Enter Description" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="received_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Received Amount</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter Received Amount" type="number" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="return_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            {...field}
                           
                            type="readyonly"
                            value={(
                              parseFloat(form.watch("total_amount") || "0") -
                              parseFloat(form.watch("received_amount") || "0")
                            ).toFixed(2)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="total_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total Amount</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Total Amount" type="number" readOnly />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="sale_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Type</FormLabel>
                        <div className="flex gap-4">
                          <label className="flex items-center gap-2">
                            <input
                              type="radio"
                              value="cash"
                              checked={field.value === "cash"}
                              onChange={() => field.onChange("cash")}
                            />
                            Cash
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="radio"
                              value="credit"
                              checked={field.value === "credit"}
                              onChange={() => field.onChange("credit")}
                            />
                            Credit
                          </label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <DottedSeparator className="my-2" />
            <div className="flex items-center justify-end gap-2">
              <Button type="button" variant="secondary" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit">Create Sale</Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default SalesForm;