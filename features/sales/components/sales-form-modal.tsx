
"use client";
import { ResponsiveModal } from "@/components/responsive-modal";
import { useSaleFormModal } from "../hooks/use-sale-form-modal";
import SalesForm from "./sales-form";

export const SaleFormModal = () => {
    const { isOpen, setIsOpen, close } = useSaleFormModal();
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen} isNormal={false}> 
            <SalesForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}