import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {  Edit, Eye, Loader2, MoreHorizontal,TimerResetIcon,Trash } from "lucide-react";
import React from "react";
import { Sale } from "../types/sales";
import { useRouter, useParams } from "next/navigation";
import { useSaleFormModal } from "../hooks/use-sale-form-modal";
import { useDeleteSale } from "../api/use-delete-sale";
import { useConfirm } from "@/hooks/use-confirm";
import { useReturnSale } from "../api/use-return-sale";
import { encryptId } from "@/lib/utils";

type Props = {
  sale: Sale;
};

export const SaleTableActions = ({ sale }: Props) => {
    const { edit } = useSaleFormModal();
    const returnSale = useReturnSale(sale.id.toString())
      
      const handleView = (id: number) => {
        window.location.href=`/sales/${encryptId(id)}`;
      };
    
     const { mutate, isPending } = useDeleteSale();
      
    const [DeletingDialog, confirmDelete] = useConfirm(
      "Delete Sale",
      "This action cannot be undone",
      "ghost"
    );
  
    const handleDelete = async () => {
      const ok = await confirmDelete();
      if (!ok) return;
      mutate(sale.id.toString(), {
        onSuccess: () => {
          console.log("sale deleted successfully");
        },
        onError: () => {
          console.error("Error deleting sale:", sale.id);
        },
      });
    };

  const router = useRouter();
  return (
    <div className="flex items-center space-x-2"> 

    <DeletingDialog />
     
      {/* <Button variant="ghost" onClick={() => edit.onOpen(sale)}>Edit</Button> */}
    <DropdownMenu>

      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions {sale.code}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => handleView(Number(sale.id))}>
            <Eye /> View
          </DropdownMenuItem>
           {/* <DropdownMenuItem onClick={() => edit(sale.id.toString())}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem> */}
          <DropdownMenuItem onClick={() => returnSale.mutate()}>
            <TimerResetIcon /> Return
            {returnSale.isPending && <Loader2 className="spin"/> }
          </DropdownMenuItem>
          <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-red-600 flex"
              disabled={isPending}
              onClick={handleDelete}
            >
              <Trash color="red" /> Delete 
            {isPending && <Loader2 className="spin"/> }
            </DropdownMenuItem>        
          </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
    </div>
  );
};