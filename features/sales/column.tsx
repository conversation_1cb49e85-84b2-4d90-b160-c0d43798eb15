"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatCurrency, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { Sale } from "./types/sales";
import { SaleTableActions } from "./components/sales-table-actions";

export const salesColumns: ColumnDef<Sale>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // {
  //   accessorKey: "id",
  //   header: ({ column }) => (
  //     <div className="flex items-center">
  //       <div>ID</div>
  //       <Button
  //         variant="ghost"
  //         size="icon"
  //         onClick={() => column.toggleSorting()}
  //       >
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     </div>
  //   ),
  //   enableSorting: true,
  // },
  {
    accessorKey: "code",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Code</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
  },
  // {
  //   accessorKey: "description",
  //   header: ({ column }) => (
  //     <div className="flex items-center">
  //       <div>Description</div>
  //       <Button
  //         variant="ghost"
  //         size="icon"
  //         onClick={() => column.toggleSorting()}
  //       >
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     </div>
  //   ),
  //   cell: ({ row }) => {
  //     return row.original.description || "N/A";
  //   },
  //   enableSorting: true,
  // },
    {
    accessorKey: "total_amount",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Total Amount</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
    cell: ({ row }) => {
      return formatCurrency(Number(row.original.total_amount));
    },
  },
  {
    accessorKey: "amount_credit",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Credit Amount</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
    cell: ({ row }) => {
      return formatCurrency(Number(row.original.amount_credit));
    },
  },
  {
    accessorKey: "customer.name",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>#customer</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Status</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Created</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return formatDate(row.original.created_at);
    },
    enableSorting: true,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Updated</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
    enableSorting: true,
  },
  {
    id: "actions",
    enableHiding: false,
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return <SaleTableActions sale={data} />;
    },
    enableSorting: false,
  },
];