import { useQuery } from "@tanstack/react-query";
import { Sales } from "../server/Sales";
import { FetchSalesRequest } from "../types/sales";

export const useFetchSales = (filters?: FetchSalesRequest)  => {
 const _filters: FetchSalesRequest = filters ?? {} as FetchSalesRequest;

  return useQuery({
    queryKey: ["sales", _filters],
    queryFn: () => Sales.getAllSales({
        search: _filters.search,
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        agent_id: _filters.agent_id,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
