import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateSaleRequest } from "../types/sales";
import { Sales } from "../server/Sales";

export const useEditSale = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateSaleRequest) => {
            const response = await Sales.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["sales"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit sales";
            toast.error(message + '');
            console.error(error);
        },
    });

    return mutation;
};