import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Sales } from "../server/Sales";

export const useDeleteSale = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Sales.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("sale record deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["sales"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete sale";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
