import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Sales } from "../server/Sales";

export const useReturnSale = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async () => {
            const response = await Sales.return(id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["sales"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to return sales";
            toast.error(message + '');
            console.error(error);
        },
    });

    return mutation;
};