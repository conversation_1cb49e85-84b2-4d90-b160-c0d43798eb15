import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateSaleRequest } from "../types/sales";
import { Sales } from "../server/Sales";

export const useCreateSale = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateSaleRequest) => {

            const response = await Sales.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["sales"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create sale";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
