import { DeleteSaleResponse, ReturnSalesResponse } from './../types/sales';
import axios from "axios";
import Izi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateSaleRequest, CreateSaleResponse, FetchSalesRequest, RetrieveSalesResponse, UpdateSaleRequest, UpdateSaleResponse } from "../types/sales";

export class Sales {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : null;

    public static async getAllSales(request: FetchSalesRequest) {
        try {
            const response = await axios.get(IziApi.sales, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }

    public static async create(form: CreateSaleRequest): Promise<CreateSaleResponse> {
        try {
            const response = await axios.post(IziApi.sales, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create sale');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleSale(id: string): Promise<RetrieveSalesResponse> {
        try {
            const response = await axios.get(`${IziApi.sales}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to retrieve sales');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async update(form: UpdateSaleRequest, id: string): Promise<UpdateSaleResponse> {
        try {
            const response = await axios.put(`${IziApi.sales}/${id}`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to update sale');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async delete(id: string): Promise<DeleteSaleResponse> {
        try {
            const response = await axios.delete(`${IziApi.sales}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 204) {
                return response.data || {};
            }
            throw new Error('Failed to delete sale');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async return(id: string): Promise<ReturnSalesResponse> {
        try {
            const response = await axios.post(`${IziApi.sales}/${id}/return`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to return sales');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
