import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';
import { useEffect } from 'react';

export const useSaleFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "sale-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState("sale-form-id", parseAsString.withDefault("").withOptions({ clearOnDefault: true }));

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  useEffect(() => {
    if (!isOpen) {
      setId("");
    }
  }, [isOpen, setId]);
  
  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}