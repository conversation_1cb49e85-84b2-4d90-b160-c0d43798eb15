import { z } from "zod";

export const fetchSalesSchema = z.object({
    search: z.string().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    agent_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    organization_id: z.string(),
});

export const createSaleSchema = z.object({
  organization_id: z.string(),
  user_id: z.string(),
  description: z.string(),
  date: z.string(),
  total_amount: z.string(),
  received_amount: z.string(),
  return_amount: z.string(),
  customer_id: z.string(),
  status:z.string(),
  sale_type: z.enum(['cash', 'credit']),
  items:z.array(
    z.object({
      inv_item_id: z.string(),
      conf_unit_id: z.string(),
      quantity: z.string(),
      unit_price: z.string(),
      inv_stock_item_id: z.string(),
      inv_stock_item_in_stock: z.string(),
    })
  ),
});