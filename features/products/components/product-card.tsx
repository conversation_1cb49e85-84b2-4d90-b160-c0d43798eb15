"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Check } from "lucide-react";
import Image from "next/image";

interface ProductCardProps {
  product: {
    id: string;
    name: string;
    description: string;
    category: string;
    image: string;
  };
  onAdd: () => void;
  isSelected: boolean;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, onAdd, isSelected }) => {
  return (
    <Card className={`group hover:shadow-md transition-all duration-200 ${isSelected ? 'ring-2 ring-green-500 bg-green-50' : ''}`}>
      <CardContent className="p-4">
        {/* Product Image */}
        <div className="relative mb-3">
          <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={product.image}
              alt={product.name}
              width={150}
              height={150}
              className="w-full h-full object-cover"
              onError={(e) => {
                // Fallback to a placeholder div if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `
                    <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span class="text-gray-400 text-sm">No Image</span>
                    </div>
                  `;
                }
              }}
            />
          </div>
          
          {/* Category Badge */}
          <Badge 
            variant="secondary" 
            className="absolute top-2 left-2 text-xs bg-white/90 backdrop-blur-sm"
          >
            {product.category}
          </Badge>

          {/* Selected Indicator */}
          {isSelected && (
            <div className="absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <Check className="w-4 h-4 text-white" />
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900 line-clamp-1">{product.name}</h3>
          <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
        </div>

        {/* Add Button */}
        <Button
          onClick={onAdd}
          disabled={isSelected}
          className={`w-full mt-3 transition-all duration-200 ${
            isSelected 
              ? 'bg-green-500 hover:bg-green-600 text-white' 
              : 'group-hover:bg-primary group-hover:text-white'
          }`}
          variant={isSelected ? "default" : "outline"}
        >
          {isSelected ? (
            <>
              <Check className="w-4 h-4 mr-2" />
              Added
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
