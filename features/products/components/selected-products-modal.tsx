"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Package, 
  Trash2, 
  DollarSign, 
  Hash, 
  Scale,
  ShoppingCart,
  TrendingUp
} from "lucide-react";
import Image from "next/image";

interface SelectedProductsModalProps {
  isOpen: boolean;
  onClose: () => void;
  products: any[];
  onRemove: (productId: string) => void;
  onFinish: () => void;
}

export const SelectedProductsModal: React.FC<SelectedProductsModalProps> = ({
  isOpen,
  onClose,
  products,
  onRemove,
  onFinish,
}) => {
  const totalValue = products.reduce((sum, product) => sum + (product.sellingPrice * product.inStock), 0);
  const totalCost = products.reduce((sum, product) => sum + (product.buyingPrice * product.inStock), 0);
  const totalProfit = totalValue - totalCost;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Selected Products ({products.length})
          </DialogTitle>
        </DialogHeader>

        {products.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">No products selected</p>
          </div>
        ) : (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <DollarSign className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Total Value</span>
                </div>
                <p className="text-lg font-bold text-blue-900">${totalValue.toFixed(2)}</p>
              </div>
              
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <Hash className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-900">Total Cost</span>
                </div>
                <p className="text-lg font-bold text-orange-900">${totalCost.toFixed(2)}</p>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Profit</span>
                </div>
                <p className="text-lg font-bold text-green-900">${totalProfit.toFixed(2)}</p>
              </div>
            </div>

            {/* Products List */}
            <ScrollArea className="max-h-96">
              <div className="space-y-3">
                {products.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    {/* Product Image */}
                    <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={product.image}
                        alt={product.name}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                          <p className="text-sm text-gray-600 truncate">{product.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {product.category}
                            </Badge>
                          </div>
                        </div>
                        
                        {/* Remove Button */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemove(product.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Product Details */}
                      <div className="grid grid-cols-4 gap-4 mt-2 text-sm">
                        <div>
                          <span className="text-gray-500">Buy:</span>
                          <p className="font-medium">${product.buyingPrice}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Sell:</span>
                          <p className="font-medium">${product.sellingPrice}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Stock:</span>
                          <p className="font-medium">{product.inStock}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Unit:</span>
                          <p className="font-medium">{product.unit}</p>
                        </div>
                      </div>

                      {/* Value Calculation */}
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                        <div className="flex justify-between">
                          <span>Total Value:</span>
                          <span className="font-medium">
                            ${(product.sellingPrice * product.inStock).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Profit Margin:</span>
                          <span className="font-medium text-green-600">
                            {((product.sellingPrice - product.buyingPrice) / product.buyingPrice * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Continue Adding
          </Button>
          {products.length > 0 && (
            <Button onClick={onFinish} className="bg-green-600 hover:bg-green-700">
              Finish Setup ({products.length} products)
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
