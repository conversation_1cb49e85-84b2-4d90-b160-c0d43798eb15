"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Package, DollarSign, Scale, Hash } from "lucide-react";
import Image from "next/image";

// Mock units data
const UNITS = [
  { value: "pcs", label: "Pieces (pcs)" },
  { value: "kg", label: "Kilograms (kg)" },
  { value: "g", label: "Grams (g)" },
  { value: "l", label: "Liters (l)" },
  { value: "ml", label: "Milliliters (ml)" },
  { value: "m", label: "Meters (m)" },
  { value: "cm", label: "Centimeters (cm)" },
  { value: "mm", label: "Millimeters (mm)" },
  { value: "box", label: "Box" },
  { value: "pack", label: "Pack" },
  { value: "bottle", label: "Bottle" },
  { value: "bag", label: "Bag" },
];

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: any;
  onAdd: (productData: any) => void;
}

export const AddProductModal: React.FC<AddProductModalProps> = ({
  isOpen,
  onClose,
  product,
  onAdd,
}) => {
  const [formData, setFormData] = useState({
    buyingPrice: "",
    sellingPrice: "",
    inStock: "",
    unit: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [imageError, setImageError] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        buyingPrice: "",
        sellingPrice: "",
        inStock: "",
        unit: "",
      });
      setErrors({});
      setImageError(false);
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.buyingPrice || parseFloat(formData.buyingPrice) <= 0) {
      newErrors.buyingPrice = "Buying price must be greater than 0";
    }

    if (!formData.sellingPrice || parseFloat(formData.sellingPrice) <= 0) {
      newErrors.sellingPrice = "Selling price must be greater than 0";
    }

    if (parseFloat(formData.sellingPrice) <= parseFloat(formData.buyingPrice)) {
      newErrors.sellingPrice = "Selling price must be higher than buying price";
    }

    if (!formData.inStock || parseInt(formData.inStock) < 0) {
      newErrors.inStock = "Stock amount must be 0 or greater";
    }

    if (!formData.unit) {
      newErrors.unit = "Please select a unit";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onAdd({
        buyingPrice: parseFloat(formData.buyingPrice),
        sellingPrice: parseFloat(formData.sellingPrice),
        inStock: parseInt(formData.inStock),
        unit: formData.unit,
        addedAt: new Date().toISOString(),
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const profitMargin = formData.buyingPrice && formData.sellingPrice 
    ? ((parseFloat(formData.sellingPrice) - parseFloat(formData.buyingPrice)) / parseFloat(formData.buyingPrice) * 100).toFixed(1)
    : "0";

  if (!product) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Add Product to Inventory
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Product Preview */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
              {!imageError ? (
                <Image
                  src={product.image}
                  alt={product.name}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-xs">No Image</span>
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
              <p className="text-sm text-gray-600 truncate">{product.description}</p>
              <Badge variant="secondary" className="text-xs mt-1">
                {product.category}
              </Badge>
            </div>
          </div>

          {/* Form Fields */}
          <div className="grid grid-cols-2 gap-4">
            {/* Buying Price */}
            <div className="space-y-2">
              <Label htmlFor="buyingPrice" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Buying Price
              </Label>
              <Input
                id="buyingPrice"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.buyingPrice}
                onChange={(e) => handleInputChange("buyingPrice", e.target.value)}
                className={errors.buyingPrice ? "border-red-500" : ""}
              />
              {errors.buyingPrice && (
                <p className="text-xs text-red-500">{errors.buyingPrice}</p>
              )}
            </div>

            {/* Selling Price */}
            <div className="space-y-2">
              <Label htmlFor="sellingPrice" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Selling Price
              </Label>
              <Input
                id="sellingPrice"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.sellingPrice}
                onChange={(e) => handleInputChange("sellingPrice", e.target.value)}
                className={errors.sellingPrice ? "border-red-500" : ""}
              />
              {errors.sellingPrice && (
                <p className="text-xs text-red-500">{errors.sellingPrice}</p>
              )}
            </div>
          </div>

          {/* Profit Margin Display */}
          {formData.buyingPrice && formData.sellingPrice && parseFloat(profitMargin) > 0 && (
            <div className="p-2 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">
                Profit Margin: <span className="font-semibold">{profitMargin}%</span>
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            {/* In Stock */}
            <div className="space-y-2">
              <Label htmlFor="inStock" className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                In Stock
              </Label>
              <Input
                id="inStock"
                type="number"
                min="0"
                placeholder="0"
                value={formData.inStock}
                onChange={(e) => handleInputChange("inStock", e.target.value)}
                className={errors.inStock ? "border-red-500" : ""}
              />
              {errors.inStock && (
                <p className="text-xs text-red-500">{errors.inStock}</p>
              )}
            </div>

            {/* Unit */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Scale className="h-4 w-4" />
                Unit
              </Label>
              <Select
                value={formData.unit}
                onValueChange={(value) => handleInputChange("unit", value)}
              >
                <SelectTrigger className={errors.unit ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select unit" />
                </SelectTrigger>
                <SelectContent>
                  {UNITS.map((unit) => (
                    <SelectItem key={unit.value} value={unit.value}>
                      {unit.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.unit && (
                <p className="text-xs text-red-500">{errors.unit}</p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Add to Inventory
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
