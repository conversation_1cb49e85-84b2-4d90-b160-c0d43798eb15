"use client";

import { SettingsSection } from "@/app/(dashboard)/settings/page";
import { StoreDetailsSettings } from "@/features/settings/components/sections/store-details-settings";
import { OrderHistorySettings } from "@/features/settings/components/sections/order-history-settings";
import { TransactionsSettings } from "./sections/transactions-settings";
import { SecuritySettings } from "./sections/security-settings";

interface SettingsContentProps {
  activeSection: SettingsSection;
}

export const SettingsContent = ({ activeSection }: SettingsContentProps) => {
  const renderContent = () => {
    switch (activeSection) {
      case "store-details":
        return <StoreDetailsSettings />;
      case "order-history":
        return <OrderHistorySettings />;
      case "transactions":
        return <TransactionsSettings />;
      case "security":
        return <SecuritySettings />;
      default:
        return <StoreDetailsSettings />;
    }
  };

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 lg:p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};
