import { z } from "zod";
export const fetchPurchaseSchema = z.object({
    query: z.string().optional(),
    search: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    total:z.string().optional()
});


export const createPurchaseSchema = z.object({
     organization_id: z.string(),
      user_id: z.string(),
      warehouse_id:z.string(),
      description: z.string(),
      date: z.string(),
      total_amount: z.string(),
      sale_type: z.enum(['cash', 'credit']),
      items:z.array(
        z.object({
          id: z.string().optional(),
          inv_item_id: z.string(),
          conf_unit_id: z.string(),
          quantity: z.string(),
          unit_price: z.string(),
    
          
        })
      ),

});