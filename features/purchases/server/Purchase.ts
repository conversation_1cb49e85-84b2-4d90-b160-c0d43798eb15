import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreatePurchaseRequest, CreatePurchaseResponse, DeletePurchaseResponse, FetchPurchaseRequest, RetrievePurchaseResponse, UpdatePurchaseRequest, UpdatePurchaseResponse } from "../types/purchases";

export class Purchases {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllPurchases(request: FetchPurchaseRequest) {
        try {
            const response = await axios.get(IziApi.purchases, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async create(form: CreatePurchaseRequest): Promise<CreatePurchaseResponse> {
            try {
                const response = await axios.post(IziApi.purchases, form, {
                    headers: {
                        Authorization: `Bear<PERSON> ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200 || response.status === 201) {
                    return response.data;
                }
                throw new Error('Failed to create purchase');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async getSinglePurchase(id: string): Promise<RetrievePurchaseResponse> {
            try {
                const response = await axios.get(`${IziApi.purchases}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });
    
                if (response.status === 200) {
                    return response.data;
                }
                throw new Error('Failed to retrieve purchases');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async update(form: UpdatePurchaseRequest, id: string): Promise<UpdatePurchaseResponse> {
            try {
                const response = await axios.put(`${IziApi.purchases}/${id}`, form, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200 || response.status === 201) {
                    return response.data;
                }
                throw new Error('Failed to update purchase');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async delete(id: string): Promise<DeletePurchaseResponse> {
            try {
                const response = await axios.delete(`${IziApi.purchases}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });
    
                if (response.status === 200 || response.status === 204) {
                    return response.data || {};
                }
                throw new Error('Failed to delete purchase');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
}
