import { create } from "zustand";

interface PurchaseViewModalState {
    id?: string;
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    open: (id: string) => void;
    close: () => void;
}

export const usePurchaseViewModal = create<PurchaseViewModalState>((set) => ({
    id: undefined,
    isOpen: false,
    setIsOpen: (open: boolean) => set({ isOpen: open }),
    open: (id: string) => set({ isOpen: true, id }),
    close: () => set({ isOpen: false, id: undefined }),
}));
