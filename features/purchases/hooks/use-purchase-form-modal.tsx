import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { useEffect } from "react";

export const usePurchaseFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "purchase-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true })
  );

  const [id, setId] = useQueryState(
    "purchase-form-id",
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);

  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  };

  useEffect(() => {
    if (!isOpen) {
      setId("");
    }
  }, [isOpen, setId]);

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
};