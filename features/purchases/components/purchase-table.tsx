import { DataTable } from "@/components/data-table";
import React, { useEffect, useState } from "react";
import { Row } from "@tanstack/react-table";
import {  FetchPurchaseRequest, FetchPurchaseResponse, Purchases, } from '../types/purchase';
import { purchaseColumns } from "../columns";
import { useFetchPurchases } from "../api/use-fetch-purchases";


interface PurchasesTableProps {
  onRowSelectedChanged?: (rows: Row<Purchases>[]) => void;
  onDataChange?: (data?: FetchPurchaseResponse | undefined) => void;
  filters?: FetchPurchaseRequest;
  organization_id: string;
}

export const PurchaseDataTable: React.FC<PurchasesTableProps> = ({
  onRowSelectedChanged,
  onDataChange,
  filters = {},
  organization_id,
}) => {
  const [request, setRequest] = useState<FetchPurchaseRequest>(filters);
  const { data: purchases , isLoading } = useFetchPurchases(request);
  const [pagination, setPagination] = useState({ pageIndex: 0,pageSize: 10 });
  const [rowSelection, setRowSelection] = useState<Row<Purchases>[]>([]);


  useEffect(() => {
    onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, onRowSelectedChanged]);

  useEffect(() => {
    onDataChange?.(purchases);
  }, [purchases, onDataChange]);

  useEffect(() => {
    setRequest((prev: any) => ({
      ...prev,
      ...filters,
    }));
  }, [filters]);

  return (
    <DataTable
      columns={purchaseColumns(organization_id)}
      data={purchases?.data ?? []}
      manualPagination
      total={purchases?.total}
      onPaginationChange={(pg) => {
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev: any) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};