
"use client";
import { ResponsiveModal } from "@/components/responsive-modal";
import { usePurchaseFormModal } from "../hooks/use-purchase-form-modal";
import PurchaseForm from "./purchase-form";


export const PurchaseFormModal = () => {
    const { isOpen, setIsOpen, close } = usePurchaseFormModal();
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen} isNormal={false}> 
           <PurchaseForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}