"use client";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate, formatCurrency } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Purchases } from "./types/purchases";
import { PurchaseTableActions } from "./components/purchase-table-actions";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";

export const purchaseColumns = (organization_id: string): ColumnDef<Purchases>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    accessorKey: "code",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Code</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },

  {
    accessorKey: "description",
    header:({column})=>(
      <div className="flex items-center">
      <div>Description</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>

    ),
    enableSorting:true
  },

  {
    accessorKey: "total_amount",
    header:({column})=>(
      <div className="flex items-center">
      <div>Total Amount</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true,
    cell: ({ row }) => {
      return formatCurrency(Number(row.original.total_amount));
    },
  },

  {
    accessorKey: "status",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Status</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>
    ),
    enableSorting:true,
    cell: ({ row }) => {
      const status = row.original.status;
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          status === 'completed' ? 'bg-green-100 text-green-800' :
          status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
          status === 'cancelled' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {status}
        </span>
      );
    },
  },
  {
    accessorKey: "created_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Created</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return formatDate(row.original.created_at);
    },
  },
  {
    accessorKey: "updated_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Updated</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
  },

  {
    
    accessorKey: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const purchase = row.original;
      return <PurchaseTableActions purchase={purchase} organization_id={organization_id}/>
    },
  },


];