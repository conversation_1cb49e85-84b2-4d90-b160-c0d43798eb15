import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreatePurchaseRequest } from "../types/purchases";
import { Purchases } from "../server/Purchase";

export const useCreatePurchase = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreatePurchaseRequest) => {

            const response = await Purchases.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["purchases"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create purchase";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
