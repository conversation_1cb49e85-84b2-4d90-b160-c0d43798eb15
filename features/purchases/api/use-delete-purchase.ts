import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Purchases } from "../server/Purchase";

export const useDeletePurchases = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Purchases.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Purchase deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["purchases"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete purchases";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
