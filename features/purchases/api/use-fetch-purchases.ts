import { useQuery } from "@tanstack/react-query";
import { FetchPurchaseRequest } from "../types/purchases";
import { Purchases } from "../server/Purchase";

export const useFetchPurchases = (filters?: FetchPurchaseRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["purchases", _filters],
    queryFn: () => Purchases.getAllPurchases({
        search: _filters.search,
        query: _filters.query,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
