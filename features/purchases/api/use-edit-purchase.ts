import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdatePurchaseRequest } from "../types/purchases";
import { Purchases } from "../server/Purchase";

export const useEditPurchase = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdatePurchaseRequest) => {
            const response = await Purchases.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["purchases"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit purchase";
            toast.error(message + '');
            console.error(error);
        },
    });

    return mutation;
};