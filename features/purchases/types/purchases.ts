import {z} from "zod";
import { fetchPurchaseSchema,createPurchaseSchema } from "../schemas";

export interface PurchaseItem {
    id: string;
    purchase_id: string;
    inv_item_id: string;
    conf_unit_id: string;
    quantity: string;
    unit_price: string;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
    inventory_item:{
      name: string;
    }
}

export interface Purchases {              
    id:number;            
    code:string;  
    date:string;       
    description :string;      
    supply_id :string;  
    status:string;      
    email :string;         
    deleted_at:string;   
    created_at :string;   
    updated_at :string ;    
    organization_id:string;
    debit_amount: string,
    total_amount: string,
    items: PurchaseItem[];

}





export type FetchPurchaseRequest=z.infer<typeof fetchPurchaseSchema>;

export interface FetchPurchaseResponse{
    data:Purchases[];
    total:number;
}

export interface RetrievePurchaseResponse {
  data: Purchases;
}

// CREATE 
export type CreatePurchaseRequest = z.infer<typeof createPurchaseSchema>;

//update
export interface CreatePurchaseResponse {
  data: Purchases;
}

// UPDATE PURCHASE
export type UpdatePurchaseRequest = z.infer<typeof createPurchaseSchema>;

export type UpdatePurchaseResponse = CreatePurchaseResponse

// DELETE PURCHASE
export type DeletePurchaseResponse = {
  data?: Purchases;
}