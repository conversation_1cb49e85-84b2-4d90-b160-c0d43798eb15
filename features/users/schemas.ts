import { z } from "zod";

export const fetchStoreUsersSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    role_id: z.string().optional(),
    sort_by: z.string().optional(),
    sort_direction: z.string().optional(),
});


export const createStoreUserSchema = z.object({
    name: z.string().min(1, "Name is required"),
    phone: z.string().min(1, "Phone number is required"),
    email: z.string().email("Invalid email address").optional(),
    role: z.string().min(1, "Role is required"), 
    organization_id: z.string().min(1,"Organization ID is required"),    
    password: z.string().min(8, "Password should be at least 8 characters").optional(),
});