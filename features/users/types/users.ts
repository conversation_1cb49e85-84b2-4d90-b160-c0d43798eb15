import { z } from "zod";
import { createStoreUserSchema, fetchStoreUsersSchema } from "../schemas";
import { Organization } from "@/features/organizations/types/organization";


export interface StoreUser {
  id: number;

  name: string;
  email: string;
  phone: string;

  role_id?: number;
  roles?: any;

  organizations: Organization[];

  created_at: string;
  updated_at: string;
  deleted_at: string;
}

//FETCH STORE USERS REQUEST AND RESPONSE
export interface FetchStoreUsersResponse {
  data: StoreUser[];
  total: number;
}

export type FetchStoreUsersRequest = z.infer<typeof fetchStoreUsersSchema>;

//CREATE STORE USER REQUEST AND RESPONSE

//RETRIEVE STORE USER REQUEST AND RESPONSE
export interface RetrieveStoreUserResponse {
  data: StoreUser;
}

// CREATE USER
export type CreateStoreUserRequest = z.infer<typeof createStoreUserSchema>;

export interface CreateStoreUserResponse {
  data: StoreUser;
}

// UPDATE USER
export type UpdateStoreUserRequest = z.infer<typeof createStoreUserSchema>;

export type UpdateStoreUserResponse = CreateStoreUserResponse

// DELETE USERS
export type DeleteStoreUserResponse = {
  data?: StoreUser;
}