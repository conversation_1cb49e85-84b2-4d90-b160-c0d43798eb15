import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { User } from "../server/User";
import { UpdateStoreUserRequest } from "../types/users";

export const useEditUser = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateStoreUserRequest) => {
            const response = await User.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit user";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};