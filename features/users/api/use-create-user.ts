import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateStoreUserRequest } from "../types/users";
import { User } from "../server/User";

export const useCreateUser = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateStoreUserRequest) => {

            const response = await User.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create user";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
