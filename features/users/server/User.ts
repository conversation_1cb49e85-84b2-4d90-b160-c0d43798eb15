import axios from "axios";
import <PERSON><PERSON><PERSON><PERSON> from "@/lib/endpoints";
import { CreateStoreUserRequest, CreateStoreUserResponse, DeleteStoreUserResponse, FetchStoreUsersRequest, RetrieveStoreUserResponse, UpdateStoreUserRequest, UpdateStoreUserResponse } from "../types/users";

export class User {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllUses(request: FetchStoreUsersRequest) {
        try {
            const response = await axios.get(IziApi.users, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }


    public static async create(form: CreateStoreUserRequest): Promise<CreateStoreUserResponse> {
                try {
                    const response = await axios.post(IziApi.users, form, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (response.status === 200 || response.status === 201) {
                        return response.data;
                    }
                    throw new Error('Failed to create user');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async getSingleUser(id: string): Promise<RetrieveStoreUserResponse> {
                try {
                    const response = await axios.get(`${IziApi.users}/${id}`, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                        },
                    });
    
                    if (response.status === 200) {
                        return response.data;
                    }
                    throw new Error('Failed to retrieve user');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async update(form: UpdateStoreUserRequest, id: string): Promise<UpdateStoreUserResponse> {
                try {
                    const response = await axios.put(`${IziApi.users}/${id}`, form, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (response.status === 200) {
                        return response.data;
                    }
                    throw new Error('Failed to update user');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
        
            public static async delete(id: string): Promise<DeleteStoreUserResponse> {
                try {
                    const response = await axios.delete(`${IziApi.users}/${id}`, {
                        headers: {
                            Authorization: `Bearer ${this.token}`,
                        },
                    });
        
                    if (response.status === 200 || response.status === 204) {
                        return response.data || {};
                    }
                    throw new Error('Failed to delete user');
                } catch (error: unknown) {
                    if (axios.isAxiosError(error) && error.response) {
                        throw error.response.data;
                    }
                    throw error;
                }
            }
}
