"use client";

import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { UserTableActions } from "@/features/users/components/user-table-actions";
import { ArrowDown, ArrowUp, ArrowUpDown, ChevronsUpDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { StoreUser } from "./types/users";

export const userColumns = (organization_id: string): ColumnDef<StoreUser>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        User
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
  },
  {
    accessorKey: "phone",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Phone
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Email
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Status
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
    cell: ({ row }) => {
      const user = row.original;
      return (
        <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium 
          ${
          user.deleted_at
        ? "bg-red-100 text-red-800"
        : "bg-green-100 text-green-800"
        }`}>
          {user.deleted_at ? "Inactive" : "Active"}
        </span>
      );
    },
  },
  {
    accessorKey: "roles.name",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Role Name</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return Array.isArray(row.original.roles) && row.original.roles.length > 0 ? row.original.roles[0].name : "N/A";
    },
    enableSorting: true,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="p-0 hover:bg-transparent"
      >
        Created
        {column.getIsSorted() === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : column.getIsSorted() === "desc" ? (
          <ArrowDown className="ml-2 h-4 w-4" />
        ) : (
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        )}
      </Button>
    ),
    cell: ({ row }) => formatDate(row.original.created_at),
  },
    {
    accessorKey: "updated_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Updated</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
  },

  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const user = row.original;

      return <UserTableActions user={user} organization_id={organization_id}/>
    },
  },
];
