import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON>, Eye, Loader2, MoreHori<PERSON>tal, Trash } from "lucide-react";
import React from "react";
import { useRouter } from "next/navigation";
import { StoreUser } from "../types/users";
import { useConfirm } from "@/hooks/use-confirm";
import { useDeleteUser } from "../api/use-delete-user";
import { useStoreUserFormModal } from "../hooks/use-user-form-modal";

type Props = {
  user: StoreUser;
  organization_id: string;
};

export const UserTableActions = ({ user }: Props) => {
  const router = useRouter();

    const { edit } = useStoreUserFormModal();

    const { mutate, isPending } = useDeleteUser();
      
        const [DeletingDialog, confirmDelete] = useConfirm(
          "Delete User",
          "This action cannot be undone",
          "ghost"
        );
      
        const handleDelete = async () => {
          const ok = await confirmDelete();
          if (!ok) return;
          mutate(user.id.toString(), {
            onSuccess: () => {
              console.log("user deleted successfully");
            },
            onError: () => {
              console.error("Error deleting user:", user.id);
            },
          });
        };
  
  return (
    <>
        
    <DeletingDialog />

    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => router.push(`/settings/users/${user.id}`)}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => edit(user.id.toString())}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          ><Trash color="red" /> Delete 
          {isPending && <Loader2 className="spin" />}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    </>

  );
};