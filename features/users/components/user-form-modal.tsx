"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { StoreUserForm } from "./user-form";
import { useStoreUserFormModal } from "../hooks/use-user-form-modal";

export const StoreUserFormModal = () => {
    const { isOpen, setIsOpen, close } = useStoreUserFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <StoreUserForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}