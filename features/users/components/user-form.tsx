"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DottedSeparator } from "@/components/dotted-separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
    Form, 
    FormField, 
    FormItem, 
    FormLabel, 
    FormControl, 
    FormMessage 
} from "@/components/ui/form";
import { Select, SelectGroup, SelectTrigger, SelectContent, SelectItem, SelectLabel, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { useEffect } from "react";
import { useRetrieveUser } from "../api/use-retrieve-user";
import { useCreateUser } from "../api/use-create-user";
import { useEditUser } from "../api/use-edit-user";
import { useStoreUserFormModal } from "../hooks/use-user-form-modal";
import { createStoreUserSchema } from "../schemas";
import { Roles } from "@/components/enums/roles-enum";

interface StoreUserFormProps {
    onCancel?: () => void;
    defaultValues?: z.infer<typeof createStoreUserSchema>;
}


export const StoreUserForm = ({ onCancel }: StoreUserFormProps) => {
    const organizationId = localStorage.getItem("current_organization") ?? undefined;
    const createUser = useCreateUser();
    const { close, id } = useStoreUserFormModal();
    const { data: user } = useRetrieveUser(id);
    const editUser = useEditUser(id);
    

    // For organization users, only allow Owner, Manager, and Staff (Shop Keeper) roles
    const roleOptions = [
        { value: Roles.Owner, label: "Owner" },
        { value: Roles.Manager, label: "Manager" },
        { value: Roles.Staff, label: "Staff" }
    ];

    const form = useForm<z.infer<typeof createStoreUserSchema>>({
        resolver: zodResolver(createStoreUserSchema),
        defaultValues: {
            name: "",
            email: "",
            phone: "",
            role: "",  
            organization_id:organizationId || "",}
    });
console.log(user?.data)

    useEffect(() => {
        if (id && user?.data) {
            const userData = user.data;

            form.setValue("name", userData.name);
            form.setValue("email", userData.email);
            form.setValue("phone", userData.phone);

            const roleName = userData.roles && Array.isArray(userData.roles) && userData.roles.length > 0
                ? userData.roles[0].name
                : userData.roles && typeof userData.roles === 'object' && userData.roles.name ? userData.roles.name : undefined;

            if (roleName) {
                // Mapping API role names to enum values
                let mappedRole = "";
                switch (roleName.toUpperCase()) {
                    case "OWNER":
                        mappedRole = Roles.Owner;
                        break;
                    case "MANAGER":
                        mappedRole = Roles.Manager;
                        break;
                    case "STAFF":
                        mappedRole = Roles.Staff;
                        break;
                    default:
                        mappedRole = roleName;
                }
                form.setValue("role", mappedRole);
            }

        }
    }, [user?.data, id, form]);

    const onSubmit = async (values: z.infer<typeof createStoreUserSchema>) => {
        if (id) {
            editUser.mutate(
                values , 
                {
                    onSuccess: () => {
                        toast.success("User updated successfully");
                        form.reset();
                        close();
                    },
                }
            );
            return;
        }
        
        createUser.mutate(
            values,
            {
                onSuccess: () => {
                    toast.success("User created successfully")
                    form.reset();
                    close();
                },
            }
        );
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    {id ? "Edit Store User" : "Create Store User"}
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} >
                        <div className="flex flex-col gap-y-4">
                            <FormField 
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Full Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter full name"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="email"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter email" 
                                                type="email"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Phone</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter phone number"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="role"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Role</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select Role" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectLabel>Roles</SelectLabel>
                                                    {roleOptions.map((role) => (
                                                        <SelectItem key={role.value} value={role.value}>
                                                            {role.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createUser.isPending}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createUser.isPending || editUser.isPending}
                            >
                                {id ? "Update User" : "Create User"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
};