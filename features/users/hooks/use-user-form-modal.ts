import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';

export const useStoreUserFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "store-user-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState(
    "store-user-form-id", 
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}