import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateSupplierRequest, CreateSupplierResponse, DeleteSupplierResponse, FetchSuppliersRequest, RetrieveSupplierResponse, UpdateSupplierRequest, UpdateSupplierResponse } from "../types/suppliers";

export class Supplier {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllSuppliers(request: FetchSuppliersRequest) {
        try {
            const response = await axios.get(IziApi.suppliers, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }


    public static async create(form: CreateSupplierRequest): Promise<CreateSupplierResponse> {
        try {
            const response = await axios.post(IziApi.suppliers, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create supplier');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleSupplier(id: string): Promise<RetrieveSupplierResponse> {
        try {
            const response = await axios.get(`${IziApi.suppliers}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to retrieve supplier');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async update(form: UpdateSupplierRequest, id: string): Promise<UpdateSupplierResponse> {
        try {
            const response = await axios.put(`${IziApi.suppliers}/${id}`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to update supplier');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async delete(id: string): Promise<DeleteSupplierResponse> {
        try {
            const response = await axios.delete(`${IziApi.suppliers}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 204) {
                return response.data || {};
            }
            throw new Error('Failed to delete supplier');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
