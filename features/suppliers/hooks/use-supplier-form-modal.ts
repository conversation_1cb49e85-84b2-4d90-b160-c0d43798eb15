import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';

export const useSupplierFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "supplier-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState(
    "supplier-form-id", 
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}