import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateSupplierRequest } from "../types/suppliers";
import { Supplier } from "../server/Supplier";

export const useCreateSupplier = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateSupplierRequest) => {

            const response = await Supplier.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["suppliers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create supplier";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
