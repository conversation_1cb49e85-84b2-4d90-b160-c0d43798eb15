import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateSupplierRequest } from "../types/suppliers";
import { Supplier } from "../server/Supplier";

export const useEditSupplier = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateSupplierRequest) => {
            const response = await Supplier.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["suppliers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit supplier";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};