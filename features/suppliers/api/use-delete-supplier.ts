import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supplier } from "../server/Supplier";

export const useDeleteSupplier = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Supplier.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Supplier deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["suppliers"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete supplier";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
