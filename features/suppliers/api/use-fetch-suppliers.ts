import { useQuery } from "@tanstack/react-query";
import { FetchSuppliersRequest } from "../types/suppliers";
import { Supplier } from "../server/Supplier";

export const useFetchSuppliers = (filters?: FetchSuppliersRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["suppliers", _filters],
    queryFn: () => Supplier.getAllSuppliers({
        query: _filters.query,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
