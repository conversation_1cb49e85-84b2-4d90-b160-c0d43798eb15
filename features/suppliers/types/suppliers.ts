import z from "zod";
import { CreateSupplierSchema, fetchSuppliersSchema } from '../schemas';

export interface Supplier {
    id: number;

    name: string;
    company: string | null;
    phone: string | null;
    address: string | null;
    organization_id: number;

    created_at: string;
    updated_at: string;
    deleted_at: string | null;
}


//FETCH supplier REQUEST AND RESPONSE
export interface FetchSuppliersResponse {
  data: Supplier[];
  total: number;
}

export type FetchSuppliersRequest = z.infer<typeof fetchSuppliersSchema>;


//RETRIEVE SUPPLIER REQUEST AND RESPONSE
export interface RetrieveSupplierResponse {
  data: Supplier;
}

// CREATE SUPPLIER
export type CreateSupplierRequest = z.infer<typeof CreateSupplierSchema>;

export interface CreateSupplierResponse {
  data: Supplier;
}

// UPDATE SUPPLIER
export type UpdateSupplierRequest = z.infer<typeof CreateSupplierSchema>;

export type UpdateSupplierResponse = CreateSupplierResponse

// DELETE SUPPLIER
export type DeleteSupplierResponse = {
  data?: Supplier;
}