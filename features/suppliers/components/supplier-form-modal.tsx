"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { useSupplierFormModal } from "../hooks/use-supplier-form-modal";
import { SupplierForm } from "./supplier-form";

export const SupplierFormModal = () => {
    const { isOpen, setIsOpen, close } = useSupplierFormModal();

    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
            <SupplierForm onCancel={() => close()} />
        </ResponsiveModal>
    )
}