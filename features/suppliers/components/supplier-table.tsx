import { DataTable } from "@/components/data-table";
import React, { useCallback } from "react";
import { Row, SortingState } from "@tanstack/react-table";
import { FetchSuppliersRequest, FetchSuppliersResponse, Supplier } from "../types/suppliers";
import { useFetchSuppliers } from "../api/use-fetch-suppliers";
import { supplierColumns } from "../columns";

interface SupplierTableProps {
  onRowSelectedChanged?: (rows: Row<Supplier>[]) => void;
  onDataChange?: (data?: FetchSuppliersResponse | undefined) => void;
  searchQuery?: string;
  filters?: FetchSuppliersRequest;
  onSearchChange?: (value: string) => void;
  organization_id: string;
}

export const SupplierTable = (props: SupplierTableProps = {} as SupplierTableProps) => {
  const [request, setRequest] = React.useState<FetchSuppliersRequest>(props.filters ?? {});
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<Supplier>[]>([]);

  const { data: suppliers, isLoading } = useFetchSuppliers(request);

  // Update request when filters or search query change
  React.useEffect(() => {
    setRequest(prev => {
      const newRequest = { ...prev };
      
      // Add filters if provided
      if (props?.filters) {
        Object.assign(newRequest, props.filters);
      }
      
      // Add search query if provided
      if (props?.searchQuery) {
        newRequest.query = props.searchQuery;
      } else if ('query' in newRequest) {
        // Remove the query property if it exists and searchQuery is empty
        delete newRequest.query;
      }
      
      return newRequest;
    });
  }, [props?.filters, props?.searchQuery]);

  // Handle sorting
  const handleSortingChange = useCallback((sortState: SortingState) => {
    setSorting(sortState);
    
    if (sortState.length > 0) {
      const { id, desc } = sortState[0];
      // Apply sorting to the request
      setRequest(prev => {
        const updatedRequest = {
          ...prev,
          sort_by: id,
          sort_direction: desc ? 'desc' as const : 'asc' as const,
        };
        return updatedRequest;
      });
    } else {
      setRequest(prev => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { sort_by, sort_direction, ...rest } = prev;
        return rest;
      });
    }
  }, []);

  // Effect to notify parent about row selection changes
  const { onRowSelectedChanged } = props || {};

  React.useEffect(() => {
    if (onRowSelectedChanged) {
      onRowSelectedChanged(rowSelection);
    }
  }, [rowSelection, onRowSelectedChanged]);

  const { onDataChange } = props || {};

  React.useEffect(() => {
    if (onDataChange && suppliers){
      onDataChange(suppliers);
    }
  }, [suppliers, onDataChange]);

  const { onDataChange: handleDataChange } = props || {};

  React.useEffect(() => {
    if (handleDataChange && suppliers) {
      handleDataChange(suppliers);
    }
  }, [suppliers, handleDataChange]);

  return (
    <DataTable
      columns={supplierColumns(props.organization_id)}
      data={suppliers?.data || []}
      manualPagination={true}
      manualSorting={true}
      total={suppliers?.total || 0}
      sorting={sorting}
      onSortingChange={handleSortingChange}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
            per_page: pg.pageSize.toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};