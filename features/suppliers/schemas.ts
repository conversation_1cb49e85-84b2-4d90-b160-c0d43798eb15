import { z } from "zod";

export const CreateSupplierSchema = z.object({
    name: z.string(),
    company: z.string().optional(),
    phone: z.string().optional(),
    address: z.string().optional(),
    organization_id: z.string(),
});

export const fetchSuppliersSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    total:z.string().optional(),
    sort_by: z.string().optional(),
    sort_direction: z.string().optional(),
});