"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatCurrency, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { InventoryItem } from "./types/inventories";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import IziApi from "@/lib/endpoints";
import { InventoryItemTableActions } from "./components/inventory-table-items";

export const inventoryItemsColumns: ColumnDef<InventoryItem>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "featured_image",
    header: "Image",
    cell: ({ row }) => {
      return (
        <Avatar className="h-10 w-10">
          <AvatarImage
            src={IziApi.getImagesUrl(row.original.featured_image ?? "")}
            alt={row.original.name}
          />
          <AvatarFallback>
            {row.original.name?.charAt(0)?.toUpperCase() || "P"}
          </AvatarFallback>
        </Avatar>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Product</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "in_stock",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>In Stock</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      return `${product.in_stock} ${product?.unit?.code}(s)`;
    },
    enableSorting: true,
  },
  {
    accessorKey: "sale_price",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Selling Price</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      return formatCurrency(Number(product.sale_price ?? 0)) ?? "N/A";
      // return product.sale_price ?? 0 ?? "N/A";
    },
    enableSorting: true,
  },
  {
    accessorKey: "unit_cost",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>#buying price</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      return formatCurrency(Number(product.stock_items?.[0]?.unit_cost ?? 0)) ?? "N/A";
      // return product.stock_items?.[0]?.unit_cost ?? 0 ?? "N/A";
    },
    enableSorting: true,
  },
  {
    accessorKey: "item_count",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>#item sold</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      return product.item_count ?? "0";
    },
    enableSorting: true,
  },
  {
    accessorKey: "revenue",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>#revenue</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      return formatCurrency(Number(product.revenue)) ?? "0";
      // return product.revenue ?? "0";
    },
    enableSorting: true,
  },
  {
    accessorKey: "profit",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>#profit</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original;
      const revenue = Number(product.revenue ?? 0);
      const unitCost = Number(product.stock_items?.[0]?.unit_cost ?? 0);
      const i_count = Number(product.item_count ?? 0);
      return formatCurrency(revenue - (i_count * unitCost));;
    },
    enableSorting: true,
  },
  {
    accessorKey: "reorder_level",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Re Order</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const product = row.original
      return product?.reorder_level ? (product?.reorder_level ?? 0) <= (product?.in_stock ?? 0) ? (
        <span className="text-green-500 bg-green-100 px-2 py-1 rounded-full">
          {product?.reorder_level}
        </span>
      ) : (
        <span className="text-red-500 bg-red-100 px-2 py-1 rounded-full">
          {product?.reorder_level}
        </span>
      ) : (
        <span className="">
          N/A
        </span>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Created</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return formatDate(row.original.created_at);
    },
    enableSorting: true,
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => (
      <div className="flex items-center">
        <div>Updated</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => column.toggleSorting()}
        >
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
    enableSorting: true,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const data = row.original;
      return <InventoryItemTableActions inventoryItem={data} />;
    },
    enableSorting: false,
  },
];