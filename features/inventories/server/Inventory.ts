import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateInventoryRequest, CreateInventoryResponse, DeleteInventoryResponse, FetchInventoryRequest, ImportInventoryItemsRequest, ImportInventoryItemsResponse, RetrieveInventoryResponse, UpdateInventoryRequest, UpdateInventoryResponse } from "../types/inventories";

export class Inventories {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : ""

    public static async getAllInventories(request: FetchInventoryRequest) {
        try {
            const response = await axios.get(IziApi.inventories, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }


    public static async create(form: CreateInventoryRequest): Promise<CreateInventoryResponse> {
        try {
            const response = await axios.post(IziApi.inventories, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create inventory');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async import(form: ImportInventoryItemsRequest): Promise<ImportInventoryItemsResponse> {
        try {
            const formData = new FormData();
            formData.append('organization_id', form.organization_id);
            formData.append('file', form.file as unknown as Blob);

            const response = await axios.post(IziApi.inventories + '/import', formData, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to import inventory');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleInventory(id: string): Promise<RetrieveInventoryResponse> {
        try {
            const response = await axios.get(`${IziApi.inventories}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to retrieve inventory');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async update(form: UpdateInventoryRequest, id: string): Promise<UpdateInventoryResponse> {
        try {
            const response = await axios.put(`${IziApi.inventories}/${id}`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to update inventory');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async delete(id: string): Promise<DeleteInventoryResponse> {
        try {
            const response = await axios.delete(`${IziApi.inventories}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200 || response.status === 204) {
                return response.data || {};
            }
            throw new Error('Failed to delete inventory');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
