import { DataTable } from "@/components/data-table";
import React from "react";
import { Row } from "@tanstack/react-table";
import { FetchInventoryRequest, FetchInventoryResponse, InventoryItem } from "../types/inventories";
import { useFetchInventories } from "../api/use-fetch-inventories";
import { inventoryItemsColumns } from "../column";

interface InventoryItemTableProps {
  onRowSelectedChanged?: (rows: Row<InventoryItem>[]) => void,
  onDataChange?: (data?: FetchInventoryResponse) => void;
  filters?: FetchInventoryRequest;
}

export const InventoryItemTable = (props?: InventoryItemTableProps) => {
  const [request, setRequet] = React.useState<FetchInventoryRequest>(props?.filters ?? {});

  const { data: inventoryItems , isLoading } = useFetchInventories(request);
 
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<InventoryItem>[]>([]);

  React.useEffect(() => {
    props?.onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, props]);

  React.useEffect(() => {
    props?.onDataChange?.(inventoryItems);
  }, [inventoryItems, props]);

  React.useEffect(() => {
    // only set the changed params of the filter, the unchaged leave as is
    setRequet((prev) => ({
      ...prev,
      ...props?.filters,
    }));
  }, [props?.filters]);

  return (
    <DataTable
      columns={inventoryItemsColumns}
      data={inventoryItems?.data ?? []}
      manualPagination={true}
      total={inventoryItems?.total}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          console.log({ pg, message: "pagination changed" });
          setPagination(pg);
          setRequet((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
        // setRequet((prev) => ({
        //   ...prev,
        //   page: pagination.pageIndex ? (pagination.pageIndex + 1).toString() : "1",
        // }));
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};