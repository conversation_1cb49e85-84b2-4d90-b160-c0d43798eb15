import { ExcelColumn, exportToExcel, formatBooleanForExcel, formatCurrencyForExcel, formatDateForExcel } from "@/lib/excel-export";

/**
 * Export inventory data to Excel
 * @param inventories - Array of inventory items to export
 */
export const exportInventoryToExcel = (inventories: any[]) => {
  const columns: ExcelColumn[] = [
    { key: 'name', header: 'Name' },
    { key: 'description', header: 'Description' },
    { key: 'inventory_item_sku', header: 'SKU' },
    { key: 'in_stock', header: 'In Stock' },
    { key: 'sale_price', header: 'Sale Price', formatter: formatCurrencyForExcel },
    { key: 'unit.name', header: 'Unit' },
    { key: 'is_product', header: 'Is Product', formatter: formatBooleanForExcel },
    { key: 'is_material', header: 'Is Material', formatter: formatBooleanForExcel },
    { key: 'is_manufactured', header: 'Is Manufactured', formatter: formatBooleanForExcel },
    { key: 'reorder_level', header: 'Reorder Level' },
    { key: 'created_at', header: 'Created At', formatter: formatDateForExcel },
    { key: 'updated_at', header: 'Updated At', formatter: formatDateForExcel },
  ];

  return exportToExcel({
    filename: 'inventory_export',
    sheetName: 'Inventory',
    columns,
    data: inventories
  });
};