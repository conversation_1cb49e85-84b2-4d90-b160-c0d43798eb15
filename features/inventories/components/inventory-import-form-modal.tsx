
"use client";
import { ResponsiveModal } from "@/components/responsive-modal";
import { useInventoryImportModal } from "../hooks/use-inventory-import-form-modal";
import InventoryImportForm from "./inventory-import-form";

export const InventoryImportFormModal = () => {
    const { isOpen, setIsOpen, close } = useInventoryImportModal();
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}> 
            <InventoryImportForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}