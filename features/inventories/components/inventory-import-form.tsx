import React, { useRef } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { DottedSeparator } from '@/components/dotted-separator';
import { Form, FormField } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { File } from 'lucide-react';
import { useParams } from 'next/navigation';
import { importInventorySchema } from '../schemas';
import { useInportInventory } from '../api/use-import-inventory';

interface InventoryItemFormProps {
  onCancel: () => void;
  defaultValues?: z.infer<typeof importInventorySchema>;
}

const InventoryImportForm = ({ onCancel }: InventoryItemFormProps) => {
  const mutation = useInportInventory();
  const organizationId = localStorage.getItem('current_organization') ?? undefined;
  const form = useForm<z.infer<typeof importInventorySchema>>({
    defaultValues: {
      organization_id: organizationId || '',
      file: undefined,
    },
  });

  const inputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      form.setValue('file', file);
      if (file.size > 5 * 1024 * 1024) {
        form.setError('file', { type: 'manual', message: 'File size exceeds 5MB' });
      } else {
        form.clearErrors('file');
      }
    }
  };

  const onSubmit = (values: z.infer<typeof importInventorySchema>) => {
    console.log(values)
    mutation.mutate(
      values ,
      {
        onSuccess: () => {
          form.reset();
          if (inputRef.current) inputRef.current.value = '';
        },
        onError: (error) => {
          console.error('Import failed:', error);
        },
      }
    );
  };

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold">Import Inventory</CardTitle>
      </CardHeader>
      <div className="px-7">
        <DottedSeparator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-y-4">
              <FormField
                control={form.control}
                name="file"
                render={() => (
                  <div className="flex flex-col gap-y-2">
                    <Avatar className="size-[72px]">
                      <AvatarFallback>
                        <File className="size-[36px] text-neutral-400" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <p className="text-sm">Inventory Excel</p>
                      <p className="text-sm text-muted-foreground">
                        CSV, XLSX, XLS, max 5mb
                      </p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      required={true}
                      ref={inputRef}
                      accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      onChange={handleImageChange}
                    />
                    <Button
                      type="button"
                      size="sm"
                      className="w-fit mt-2"
                      onClick={() => inputRef.current?.click()}
                    >
                      Upload File
                    </Button>
                    {form.watch('file') && (
                      <p className="text-sm text-green-600 mt-2">
                        File uploaded: {form.watch('file')?.name}
                      </p>
                    )}
                    {form.formState.errors.file && (
                      <p className="text-sm text-red-600 mt-2">
                        {form.formState.errors.file.message}
                      </p>
                    )}
                  </div>
                )}
              />
            </div>
            <DottedSeparator className="py-7" />
            <div className="flex items-center justify-between">
              <Button disabled={mutation.isPending} type="button" variant="secondary" onClick={onCancel}>
                Cancel
              </Button>
              <Button disabled={mutation.isPending} type="submit">Import Inventory</Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default InventoryImportForm;