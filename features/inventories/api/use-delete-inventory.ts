import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Inventories } from "../server/Inventory";

export const useDeleteInventory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Inventories.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("inventory deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["expenses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete inventoory";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
