import { useQuery } from "@tanstack/react-query";
import { FetchInventoryRequest } from "../types/inventories";
import { Inventories } from "../server/Inventory";

export const useFetchInventories = (filters?: FetchInventoryRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["inventories", _filters],
    queryFn: () => Inventories.getAllInventories({
        query: _filters.query,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
