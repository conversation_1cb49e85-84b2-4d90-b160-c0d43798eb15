import { UpdateInventoryRequest } from './../types/inventories';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Inventories } from "../server/Inventory";

export const useEditExpense = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateInventoryRequest) => {
            const response = await Inventories.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["inventories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit inventory";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};