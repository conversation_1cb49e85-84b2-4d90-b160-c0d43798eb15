import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Inventories } from "../server/Inventory";
import { ImportInventoryItemsRequest } from "../types/inventories";

export const useInportInventory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: ImportInventoryItemsRequest) => {

            const response = await Inventories.import(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["inventories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to import inventory";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
