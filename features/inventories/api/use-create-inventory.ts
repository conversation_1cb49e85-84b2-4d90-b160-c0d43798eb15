import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateInventoryRequest } from "../types/inventories";
import { Inventories } from "../server/Inventory";

export const useCreateInventory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateInventoryRequest) => {

            const response = await Inventories.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["inventories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create inventory";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
