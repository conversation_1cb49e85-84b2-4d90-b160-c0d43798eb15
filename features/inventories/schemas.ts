import { z } from "zod";


export const fetchInventoryItemsSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional()
});

export const importInventorySchema = z.object({
  organization_id: z.string().nonempty("Organization ID is required"),
  file: z
    .instanceof(File)
    .refine(
      (file) =>
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "text/csv" ||
        file.type === "application/vnd.ms-excel",
      {
        message: "File must be an Excel or CSV file",
      }
    )
    .refine((file) => file.size <= 5 * 1024 * 1024, {
      message: "File size must be less than 5MB",
    })
    .refine(
      (file) =>
        file.name.endsWith(".xlsx") ||
        file.name.endsWith(".xls") ||
        file.name.endsWith(".csv"),
      {
        message: "File must have .xlsx, .xls, or .csv extension",
      }
    ),
});

export const createInventorySchema = z.object({
  name: z.string().nonempty("Name is required"),
  featured_image: z.any().optional(),
  description: z.string().optional(),
  inventory_item_sku: z.string().nonempty("SKU is required"),
  sale_price: z.string().nonempty("Sale price is required"),
  reorder_level: z.string().nonempty("Reorder level is required"),
  inventory_category_id: z.string().nonempty("Category is required"),
  unit_type_id: z.string().nonempty("Unit type is required"),
  default_unit_id: z.string().nonempty("Default unit is required"),
  organization_id: z.string().nonempty("Organization ID is required"),
  is_material: z.string().optional(),
  is_manufactured: z.string().optional(),
  is_product: z.string().optional(),
  units: z.array(z.object({
    id: z.string(),
    name: z.string(),
    factor: z.string(),
    code: z.string(),
    symbol: z.string().optional(),
    unit_type_id: z.string(),
  })),
});