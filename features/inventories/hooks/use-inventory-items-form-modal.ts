    import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';

export const useInventoryItemFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "inventory-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState(
    "inventory-form-id", 
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}