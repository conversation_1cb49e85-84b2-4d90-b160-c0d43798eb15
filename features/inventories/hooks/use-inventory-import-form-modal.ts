import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs';

export const useInventoryImportModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "inventory-import",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true }),
  );

  const [id, setId] = useQueryState(
    "inventory-import", 
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const edit = (id: string) => {
    setId(id);
    setIsOpen(true);
  }

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
    edit,
  };
}