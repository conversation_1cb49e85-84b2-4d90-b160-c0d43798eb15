import { Organization } from "@/features/organizations/types/organization";
import { z } from "zod";
import { createInventorySchema, fetchInventoryItemsSchema, importInventorySchema } from "../schemas";

interface StockItem {
  id?: number;

  inv_item_id?: number;
  source_id?: number;
  source_type?: string;
  inv_warehouse_id?: number;
  
  quantity?: number;
  unit_cost?: string;
  in_stock?: number;
  unit_id?: number;

  deleted_at?: string | null;
  created_at?: string;
  updated_at?: string;
}


export interface InventoryItem {
    id: number;

    inventory_item_sku?: string;
    organization_id?: bigint;
    organization?: Organization;
    
    product_id?: bigint;
    product?: any;

    inventory_category_id?: bigint;

    name?: string;
    description?: string;

    unit_type_id?: bigint;

    is_manufactured?: boolean;
    is_material?: boolean;
    is_product?: boolean;

    item_count: string;
    revenue: string;
    profit: string;

    unit_id?: bigint;
    units?: Unit;
    

    reorder_level?: number;
    in_stock?: number;
    featured_image?: string;

    sale_price?: number;

    stock_items?: StockItem[];

    deleted_at?: string;
    created_at: string;
    updated_at: string;
}


// FETCH INVENTORY REQUEST AND RESPONSE
export type FetchInventoryRequest = z.infer<typeof fetchInventoryItemsSchema>;

// Fetch inventory import request and response
export type ImportInventoryItemsRequest = z.infer<typeof importInventorySchema>;
export type ExportInventoryItemsRequest = z.infer<typeof fetchInventoryItemsSchema>;

export interface ImportInventoryItemsResponse {
  success: boolean;
  message: string;
  errors?: Array<{ row: number; error: string }>;
}

export interface ExportInventoryItemsResponse{
  data: string; 
  filename: string;
}
  

export interface FetchInventoryResponse {      
    data: InventoryItem[];
    total: number;
}

export interface UnitType {
    id?: number;
    name?: string;
    description?: string | null;
    created_at?: string;
    updated_at?: string;

    // relationships
    units?: Unit[];
}

export interface Unit {
    id?: number;
    unit_type_id?: number;
    name?: string;
    description?: string | null;
    code?: string;
    symbol?: string | null;
    factor?: number;
    deleted_at?: string | null;
    created_at?: string;
    updated_at?: string;
    organization_id?: number | null;
    product_id?: number | null;
    unitable_id?: number | null;
    unitable_type?: string | null;

    // relationships
    unit_type?: UnitType;
}


// Create product
export type CreateInventoryRequest = z.infer<typeof createInventorySchema>;

export interface CreateInventoryResponse {
  data: InventoryItem;
}

// Update product
export type UpdateInventoryRequest = z.infer<typeof createInventorySchema>;

// use the same response as create product
export type UpdateInventoryResponse = CreateInventoryResponse;

// Retrieve Organization
export interface RetrieveInventoryResponse {
    data: InventoryItem;
  }
  

// Delete invenstory item
export interface DeleteInventoryResponse {
    data?: InventoryItem;
  }