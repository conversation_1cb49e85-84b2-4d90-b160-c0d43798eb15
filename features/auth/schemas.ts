import { z } from "zod";

export const loginSchema = z.object({
    phone: z.string().min(10, "Phone number is required"),
    password: z.string().min(1, "Password is required"),
});

export const registerSchema = z.object({
    name: z.string().min(1, "Name is required"),
    phone: z.string().min(10, "Phone number is required"),
    password: z.string().min(8, "Password must be at least 8 characters"),
});
