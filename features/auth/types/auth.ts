

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
//   roles: Role[];
 roles: any[];
//   organizations: Organization[];
 organizations: any[];

  created_at: string;
  updated_at: string;
  deleted_at: string;
}

// REQUEST AND RESPONSE TYPES

export interface LoginRequest {
  phone: string;
  password: string;
}

export interface LoginResponse {
  data: {
    token: string;
    user: User;
  }
}