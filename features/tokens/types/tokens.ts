import { Organization } from "@/features/organizations/types/organization";

export type BuyTokensRequest = {
  amount: string;
  phone: string;
};

export type AddTokenPurchase = {
  id: string;

  organization_id: string;

  user_id: string;

  phone: string;
  amount: string;
  status: string;

  created_at: string;
  updated_at: string;
};

export type Transaction = {
  id: string;

  transactable_id: string;
  transactable_type: string;

  type: string;
  status: string;
  amount: string;
  reference: string;
  currency: string;
  account: string;
  meta: string;
  
  created_at: string;
  updated_at: string;
};

export type BuyTokensResponse = {
  add_token_purchase: AddTokenPurchase;
  transaction: Transaction;
  organization: Organization;
};