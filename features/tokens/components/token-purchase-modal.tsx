"use client";

import { ResponsiveModal } from "@/components/responsive-modal";
import { useTokenPurchaseModal } from "../hooks/use-token-purchase-modal";
import { TokenPurchaseForm } from "./token-purchase-form";

export const TokenPurchaseModal = () => {
  const { isOpen, setIsOpen, close } = useTokenPurchaseModal();

  return (
    <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
      <TokenPurchaseForm onCancel={() => close()} />
    </ResponsiveModal>
  );
};
