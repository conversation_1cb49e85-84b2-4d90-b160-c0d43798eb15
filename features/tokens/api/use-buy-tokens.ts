import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { BuyTokensRequest } from "../types/tokens";
import { Tokens } from "../serve/Token";

export const useBuyTokens = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: BuyTokensRequest) => {
            const response = await Tokens.buy(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["tokens"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to buy tokens";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};