import z from "zod";
import { FetchCreditRequest, fetchProductSchema, fetchReportSchema } from "../schema";



export interface ExpenseTrend {
  period: string; 
  number_of_expenses: number;
  total_amount: string; 
}


export interface ExpenseCategory {
  category_name: string;
  trend: ExpenseTrend[];
  total_amount: number;
}


export interface FetchExpensesTrendResponse {
  data: ExpenseCategory[];
  total_expenses: number;
}

export interface TrendData {
     transaction_date: string;
      total_expenses?: string;
      total_purchases?: string;
     total_sales?: string;
}



export interface ReportData {
  total_sales: string;
  total_purchases: string;
  total_expenses: string;
  token_consumption: number;
  total_inventoryItems: number;
  total_gross: number;
  net_profit: number;
  cost_of_goods_sold: number;
  sales_trends: TrendData[];
  purchases_trends: TrendData[];
  expenses_trends: TrendData[];
}

export interface FetchBusinessPerformanceResponse {
  data: ReportData;
}

export type FetchExpenseRequest = z.infer<typeof fetchReportSchema>;

export type FetchBusinessPerformanceRequest = z.infer<typeof fetchReportSchema>;

export type FetchInventoryStockRequest = z.infer<typeof fetchReportSchema>;

export type FetchSalesReportRequest = z.infer<typeof fetchReportSchema>;

export type FetchCreditReportRequest = z.infer<typeof FetchCreditRequest>;

export type FetchProductRequest = z.infer<typeof fetchProductSchema>;

export interface DashboardStats {
  organization: {
    id: number;
    name: string;
    created_at: string; // ISO date string
  };
  user_stats: {
    total_users: number;
  };
  product_stats: {
    total_products: number;
    low_stock_products: number;
    top_products_by_quantity: {
      id: number;
      name: string;
      total_quantity_sold: number;
      current_stock: number | null;
    }[];
    top_products_by_revenue: {
      id: number;
      name: string;
      total_revenue: number;
      profit_margin: number | null;
    }[];
  };
  customer_stats: {
    total_customers: number;
    new_customers_last_30_days: number;
    customer_growth_rate: number;
    top_customers_by_frequency: {
      id: number;
      name: string;
      total_orders: number;
      last_order_date: string; // ISO date string
    }[];
    top_customers_by_spending: {
      id: number;
      name: string;
      total_spending: string; // string to preserve formatting
      average_order_value: string; // string to preserve formatting
    }[];
  };
  token_stats: {
    current_token_balance: number;
    tokens_consumed_last_30_days: number;
    tokens_purchased_last_30_days: number;
    estimated_days_remaining: number | null;
  };
  financial_overview: {
    total_sales_last_30_days: number;
    total_purchases_last_30_days: number;
    total_expenses_last_30_days: number;
    gross_profit_last_30_days: number;
    net_profit_last_30_days: number;
    gross_profit_margin: number;
    net_profit_margin: number;
  };
}
export interface StockItem {
  item_name: string;
  sku: string;
  total_stock: number;
  reorder_level: number | null;
}

// Type definitions for unit type
export interface UnitType {
  unit_type_id: number;
  unit_type_name: string;
  total_stock_unit: number;
  total_stock_value: number;
}

export interface ProductData {
  id: string | number;
  name: string;
  featured_image?: string;
  inventory_item_sku?: string;
  sale_price: string;
  no_sold?: number;
  revenue: number | string;
  profit: number | string;
  in_stock: number;
  reorder_level?: number;
  inventory_category_id?: string | number;
}