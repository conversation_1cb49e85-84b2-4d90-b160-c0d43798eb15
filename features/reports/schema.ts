import z from "zod";

export const fetchReportSchema = z.object({
    search: z.string().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    organization_id: z.string(),
});

export const fetchProductSchema = z.object({
    search: z.string().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    organization_id: z.number,
});

export const FetchCreditRequest = z.object({
    organization_id: z.number(),
});