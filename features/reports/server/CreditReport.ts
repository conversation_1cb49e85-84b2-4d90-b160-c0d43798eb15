import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchCreditReportRequest } from "../types/report";

export class CreditReport {
    private static token = localStorage.getItem("izi_token");

    public static async getCreditReport(request:FetchCreditReportRequest) {
        try {
            const response = await axios.get(IziApi.creditReport, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
