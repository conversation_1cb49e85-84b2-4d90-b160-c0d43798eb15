import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchBusinessPerformanceRequest } from "../types/report";




export class BusinessReport {
    private static token = localStorage.getItem("izi_token");

    public static async getallBusinessReport(request:FetchBusinessPerformanceRequest ) {
        try {
            const response = await axios.get(IziApi.buniseeReports, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
