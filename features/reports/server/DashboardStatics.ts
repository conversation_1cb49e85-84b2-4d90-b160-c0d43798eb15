import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";

export class DashboardStatPage {
    private static token = localStorage.getItem("izi_token");

    public static async getDashboardStatic(id: number) {
        try {
            const response = await axios.get(IziApi.dashboardStats(id), {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });
       //     console.log("check server",response.data);

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
               // console.error("Error fetching dashboard stats:", error.response.data);
                throw error.response.data;
            }
            throw error;
        }
    }
}
