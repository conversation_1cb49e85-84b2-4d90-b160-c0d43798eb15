import axios from "axios";
import <PERSON><PERSON><PERSON><PERSON> from "@/lib/endpoints";
import { FetchSalesReportRequest } from "../types/report";
export class SalesReport {
    private static token = localStorage.getItem("izi_token");

    public static async getallSalesReport(request:FetchSalesReportRequest ) {
        try {
            const response = await axios.get(IziApi.salesReport, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });
             console.log("check server==>data==>",response.data);
            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
