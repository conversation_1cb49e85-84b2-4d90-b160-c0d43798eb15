import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import {   FetchProductRequest } from "../types/report";



export class ProductPerformance {
    private static token = localStorage.getItem("izi_token");

    public static async getProductPerformanceReport(request:FetchProductRequest ) {
        try {
            const response = await axios.get(IziApi.productPerformanceReport, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });
        console.log("Product Performance Report:===========>", response.data);
            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
         //   console.error("Error fetching product performance report:====>", error);
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
