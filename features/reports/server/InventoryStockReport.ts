import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import {  FetchInventoryStockRequest } from "../types/report";



export class InventoryStokReport {
    private static token = localStorage.getItem("izi_token");

    public static async getallInventoryStockReport(request:FetchInventoryStockRequest ) {
        try {
            const response = await axios.get(IziApi.inventoryStockReport, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }
}
