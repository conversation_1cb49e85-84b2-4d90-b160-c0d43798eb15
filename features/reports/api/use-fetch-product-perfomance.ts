import { useQuery } from "@tanstack/react-query";
import {   FetchProductRequest } from "../types/report";
import { ProductPerformance } from "../server/ProductPreformance";




export const useFetchProductReport = (filters?: FetchProductRequest)  => {
 const _filters: FetchProductRequest = filters ?? {} as FetchProductRequest;

  return useQuery({
    queryKey: ["product-report", _filters],
    queryFn: () => ProductPerformance.getProductPerformanceReport({
        page: _filters.page,
        search: _filters.search,
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        organization_id: _filters.organization_id,
    }),
 });
};
