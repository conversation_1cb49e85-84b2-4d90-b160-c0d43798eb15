import { useQuery } from "@tanstack/react-query";
import {  FetchInventoryStockRequest } from "../types/report";
import { InventoryStokReport } from "../server/InventoryStockReport";



export const useFetchStockReport = (filters?: FetchInventoryStockRequest)  => {
 const _filters: FetchInventoryStockRequest = filters ?? {} as FetchInventoryStockRequest;

  return useQuery({
    queryKey: ["inventory-stock-report", _filters],
    queryFn: () => InventoryStokReport.getallInventoryStockReport({
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
