import { useQuery } from "@tanstack/react-query"; 
import { FetchBusinessPerformanceRequest } from "../types/report";
import { BusinessReport } from "../server/BusinessReport";



export const useFetchBusinessReport = (filters?: FetchBusinessPerformanceRequest)  => {
 const _filters: FetchBusinessPerformanceRequest = filters ?? {} as FetchBusinessPerformanceRequest;

  return useQuery({
    queryKey: ["business-report", _filters],
    queryFn: () => BusinessReport.getallBusinessReport({
        search: _filters.search,
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
