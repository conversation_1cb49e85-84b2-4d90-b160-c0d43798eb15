import { useQuery } from "@tanstack/react-query";
import { CreditReport } from "../server/CreditReport";
import { FetchCreditReportRequest } from "../types/report";




export const useFetchCreditsReport = (filters?: FetchCreditReportRequest)  => {
 const _filters: FetchCreditReportRequest = filters ?? {} as FetchCreditReportRequest;

  return useQuery({
    queryKey: ["credit-report", _filters],
    queryFn: () => CreditReport.getCreditReport(
        {
            organization_id: _filters.organization_id,
        }
    ),
 });
};
