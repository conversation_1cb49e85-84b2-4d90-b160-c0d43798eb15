import { useQuery } from "@tanstack/react-query";
import { FetchSalesReportRequest } from "../types/report";
import { SalesReport } from "../server/SalesReport";

export const useFetchSalesReport = (filters?: FetchSalesReportRequest)  => {
 const _filters: FetchSalesReportRequest = filters ?? {} as FetchSalesReportRequest;

  return useQuery({
    queryKey: ["sales-report", _filters],
    queryFn: () => SalesReport.getallSalesReport({
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        organization_id: _filters.organization_id,
    }),
 });
};
