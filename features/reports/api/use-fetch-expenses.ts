import { useQuery } from "@tanstack/react-query";
import { ExpensesReport } from "../server/ExpensesReport"; 
import { FetchExpenseRequest } from "../types/report";



export const useFetchExpensesReport = (filters?: FetchExpenseRequest)  => {
 const _filters: FetchExpenseRequest = filters ?? {} as FetchExpenseRequest;

  return useQuery({
    queryKey: ["expenses-report", _filters],
    queryFn: () => ExpensesReport.getallExpensesReport({
        search: _filters.search,
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
