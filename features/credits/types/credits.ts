import { z } from "zod";
import { AgingSchema, fetchCreditSchema, paymentFormSchema } from "../schemas";

// Credit status enum
export enum CreditStatus {
    PENDING = 'pending',
    PARTIAL = 'partial',
    PAID = 'paid',
  }
  
  // Payment method enum
  export enum PaymentMethod {
    CASH = 'cash',
    BANK_TRANSFER = 'bank_transfer',
    LIPA_NA_MPESA = 'lipa_na_mpesa',
    MOBILE_MONEY = 'mobile_money',
  }

  // Credit request parameters
 export type FetchCreditRequest=z.infer<typeof fetchCreditSchema>;
 export type FetchAgingReportRequest = z.infer<typeof AgingSchema>;
 export type CreatePaymentCreditRequest = z.infer<typeof paymentFormSchema>;



 export interface FetchAgingReportResponse {
    data: CreditAgingReport;
    total: number;
 }

 export interface FetchCreditResponse{
    
    data: Credits[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    
 }

 export interface RetrieveCreditResponse {
  data: Credits;
 }
  
 export interface CreatePaymentCreditResponse {
  data: Credits;
 }


  
  // Credit model
  export interface Credits {
    id: number;
    reference: string;
    date: string;
    organization_id: number;
    creditable_type: string;
    creditable_id: number;
    debtor_type: string;
    debtor_id: number;
    creditor_type: string;
    creditor_id: number;
    amount: string;
    balance: string;
    status: CreditStatus;
    notes?: string;
    repayments: CreditRepayment[];
    created_at?: string;
    updated_at?: string;
    // Relations
    creditable?: string; // Replace with the actual structure of creditable
    creditor?: string; // Replace with the actual structure of creditor
  }
  
  // Credit repayment model
  export interface CreditRepayment {
    id: number;
    reference: string;
    date: string;
    organization_id: number;
    credit_id: number;
    payment_id: number;
    amount: number;
    balance: number;
    notes?: string;
    payment?: Payment;
    created_at?: string;
    updated_at?: string;
  }
  
  // Payment model
  export interface Payment {
    id: number;
    reference: string;
    date: string;
    organization_id: number;
    payor_type: string;
    payor_id: number;
    payee_type: string;
    payee_id: number;
    amount: number;
    method: PaymentMethod;
    status: string;
    meta?: Record<string, unknown>;
    created_at?: string;
    updated_at?: string;
  }
  
  // Credit aging report
  export interface CreditAgingReport {
    organization_id:number;
    current: number;        // 0-30 days
    thirty_days: number;    // 30-60 days
    sixty_days: number;     // 60-90 days
    ninety_plus_days: number; // 90+ days
    total: number;
  }


  
  // Customer credits response
  export interface CustomerCreditsResponse {
    credits: Credits[];
    total_owed: number;
  }
  
  // API response wrapper
  export interface ApiResponse<T> {
    success: boolean;
    data: T;
    message?: string;
    errors?: Record<string, string[]>;
  }