import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { useEffect } from "react";

export const useCreditFormModal = () => {
  const [isOpen, setIsOpen] = useQueryState(
    "payment-form",
    parseAsBoolean.withDefault(false).withOptions({ clearOnDefault: true })
  );

  const [id, setId] = useQueryState(
    "payment-form-id",
    parseAsString.withDefault("").withOptions({ clearOnDefault: true })
  );

  const close = () => setIsOpen(false);

  const open = (id:string) => {
    setId(id);
    setIsOpen(true);
  };

  useEffect(() => {
    if (!isOpen) {
      setId("");
    }
  }, [isOpen, setId]);

  return {
    id,
    isOpen,
    open,
    close,
    setIsOpen,
 
    
    
  };
};