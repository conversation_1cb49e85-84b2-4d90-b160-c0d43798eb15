import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreatePaymentCreditRequest, FetchAgingReportRequest, FetchCreditRequest } from "../types/credits";

export class Credits {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllCredits(request: FetchCreditRequest) {
        try {
            const response = await axios.get(IziApi.credits, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getAgingReport(request: FetchAgingReportRequest) {
        try {
            const response = await axios.get(IziApi.credits + '/reports/aging', {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        }
        catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async getSingleCredit(id: string) {
        try {
            const response = await axios.get(`${IziApi.credits}/${id}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data;
            }
            throw new Error('Failed to retrieve credit');
        }
        catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async createPayment(form: CreatePaymentCreditRequest, id: string) {
        try {
            const response = await axios.post(`${IziApi.credits}/${id}/payments`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });
            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to create payment');
        }
        catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }


}