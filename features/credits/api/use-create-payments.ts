import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreatePaymentCreditRequest } from "../types/credits";
import { Credits } from "../server/Credit";

export const useCreatePayments = (id: string) => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreatePaymentCreditRequest) => {

            const response = await Credits.createPayment(form, id);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["credits"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create payments";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
