import { useQuery } from "@tanstack/react-query";
import { FetchAgingReportRequest } from "../types/credits";
import { Credits } from "../server/Credit";

export const useFetchAging = (filters?: FetchAgingReportRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["credits", _filters],
    queryFn: () => Credits.getAllCredits({
        organization_id: _filters.organization_id,
    }),
 });
};
