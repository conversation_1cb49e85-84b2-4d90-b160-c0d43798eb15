import { useQuery } from "@tanstack/react-query";
import { FetchCreditRequest } from "../types/credits";
import { Credits } from "../server/Credit";

export const useFetchCredits = (filters?: FetchCreditRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["credits", _filters],
    queryFn: () => Credits.getAllCredits({
        query: _filters.query,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
