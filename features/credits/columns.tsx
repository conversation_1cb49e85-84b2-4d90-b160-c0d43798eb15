"use client";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { Credits } from "./types/credits";
import { CreditTableActions } from "./components/credits-table-actions";

export const creditColumns = (organization_id: string): ColumnDef<Credits>[] => [
  {
    
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey:"creditor.name",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Customer</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },
  {
    accessorKey:"creditable.code",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Sales Code</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },

  {
    accessorKey:"amount",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Amount</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },

  {
    accessorKey:"balance",
    header:({column})=>(
      <div className="flex items-center">
      <div>Balance</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>

    ),
    enableSorting:true
  },

  {
    accessorKey:"status",
    header:({column})=>(
      <div className="flex items-center">
      <div>Status</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },
  {
    accessorKey:"created_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Created</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return formatDate(row.original.created_at ?? "");
    },
  },
  {
    accessorKey:"updated_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Updated</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at ?? "");
    },
  },

  {
    
    accessorKey:"actions",
    header: "Actions",
    cell: ({ row }) => {
      const credit = row.original;
      return <CreditTableActions credit={credit} organization_id={organization_id}/>
    },
  },


];