import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {  Eye,MoreHorizontal, PlusCircle } from "lucide-react";
import { Credits } from "../types/credits";
import { useCreditFormModal } from "../hooks/use-payment-form-modal";
import { encryptId } from "@/lib/utils";


type Props = {
  credit: Credits;
  organization_id: string;
};

export const CreditTableActions = ({ credit }: Props) => {
 const {open} = useCreditFormModal();

  const handleViewCredit=(id:number)=>{

    window.location.href=`/credits/${encryptId(id)}`;

  }



  return (
    <div className="flex items-center space-x-2"> 
    
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{credit.reference}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
             className=" text-blue-400 flex"
             onClick={() => handleViewCredit(credit.id)}
             >
              <Eye/> View 
            </DropdownMenuItem>

            <DropdownMenuItem
             className="text-red-500 flex"
             onClick={()=>open(String(credit.id))} 
            >
              <PlusCircle/> Repayment
            </DropdownMenuItem>

          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};