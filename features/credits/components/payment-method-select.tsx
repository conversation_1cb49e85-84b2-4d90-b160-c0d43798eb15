"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PaymentMethod } from "../types/credits";

interface PaymentMethodSelectProps {
  value: PaymentMethod;
  onValueChange: (value: PaymentMethod) => void;
  disabled?: boolean;
}

export const PaymentMethodSelect = ({ 
  value, 
  onValueChange, 
  disabled 
}: PaymentMethodSelectProps) => {
  const paymentMethods = [
    { value: PaymentMethod.CASH, label: "Cash" },
    { value: PaymentMethod.BANK_TRANSFER, label: "Bank Transfer" },
    { value: PaymentMethod.LIPA_NA_MPESA, label: "Lipa na M-Pesa" },
    { value: PaymentMethod.MOBILE_MONEY, label: "Mobile Money" },
  ];

  return (
    <Select 
      value={value} 
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select payment method" />
      </SelectTrigger>
      <SelectContent>
        {paymentMethods.map((method) => (
          <SelectItem key={method.value} value={method.value}>
            {method.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
