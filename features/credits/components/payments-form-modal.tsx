
"use client";

import { ResponsiveModal } from "@/components/responsive-modal";
import { useCreditFormModal } from "../hooks/use-payment-form-modal";
import { CreditForm } from "./payments-form";

export const CreditPaymentFormModal = () => {
    const { isOpen, setIsOpen, close} = useCreditFormModal()
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}> 
            <CreditForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}