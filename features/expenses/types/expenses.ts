import { z } from "zod";
import { createExpenseSchema, fetchExpenseSchema } from "../schemas";
import { ExpenseCategory } from "@/features/expense_category/types/inventoryCategory";

export interface Expense {
    id: number;
    creator_id: number | null;
    expensable_type: string | null; 
    expensable_id: number | null;
    date: string;      
    description: string;
    amount: string;       
    created_at: string;   
    updated_at: string;   
    organization_id: string;

    expense_category_id: number | null;
    category?: ExpenseCategory;
  }

  // REQUEST AND RESPONSE TYPES
  
  // Fetch expenses
  export type FetchExpensesRequest = z.infer<typeof fetchExpenseSchema>;
  
  export interface FetchExpensesResponse {
    data: Expense[];
    total: number;
  }

  // Create expense
  export type CreateExpenseRequest = z.infer<typeof createExpenseSchema>;
  
  export interface CreateExpenseResponse {
    data: Expense;
  }

  // update expense
  export type UpdateExpenseRequest = z.infer<typeof createExpenseSchema>;
  
  export type UpdateExpenseResponse = CreateExpenseResponse
  
  export interface RetrieveExpensesResponse {
    data: Expense;
  }

  // delete expense
  export type DeleteExpenseResponse = {
    data?: Expense;
  }