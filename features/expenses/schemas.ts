import { z } from "zod";

export const fetchExpenseSchema = z.object({
    expense_id: z.string().optional(),
    search: z.string().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    creator_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    organization_id: z.string().optional(),
});


export const createExpenseSchema = z.object({
  organization_id: z.string(),
  description: z.string(),
  amount: z.string(),
  expensable_type: z.string().optional(),       
  date: z.string().optional(),       
  creator_id: z.string().optional(), 
  expense_category_id: z.string().optional(),      
});

