"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { ExpenseTableActions } from "./components/expense-table-actions";
import { Expense } from "./types/expenses";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";

export const expenseColumns: ColumnDef<Expense>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
   {
    accessorKey: "date",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Date</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true,
    cell: ({ row }) => {
      return formatDate(row.original.date);
    },

  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => row.original.category?.name ?? "",
  },
  {
    accessorKey: "amount",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Amount</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },
  {
    accessorKey: "description",
    header: "Description",
  },
 
  {
    accessorKey: "created_at",
    header: "Created At",
    cell: ({ row }) => {
      return formatDate(row.original.created_at);
    },
  },
  {
    accessorKey: "updated_at",
    header: "Updated At",
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const expense = row.original;

      return <ExpenseTableActions expense={expense} />
    },
  },
];