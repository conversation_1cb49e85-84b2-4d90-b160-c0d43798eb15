import axios from "axios";
import Izi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateExpenseRequest, CreateExpenseResponse, DeleteExpenseResponse, FetchExpensesRequest, RetrieveExpensesResponse, UpdateExpenseRequest, UpdateExpenseResponse } from "../types/expenses";

export class Expenses {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllExpenses(request: FetchExpensesRequest) {
        try {
            const response = await axios.get(IziApi.expenses, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

public static async create(form: CreateExpenseRequest): Promise<CreateExpenseResponse> {
            try {
                const response = await axios.post(IziApi.expenses, form, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200 || response.status === 201) {
                    return response.data;
                }
                throw new Error('Failed to create expense');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async getSingleExpense(id: string): Promise<RetrieveExpensesResponse> {
            try {
                const response = await axios.get(`${IziApi.expenses}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });

                if (response.status === 200) {
                    return response.data;
                }
                throw new Error('Failed to retrieve expense');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async update(form: UpdateExpenseRequest, id: string): Promise<UpdateExpenseResponse> {
            try {
                const response = await axios.put(`${IziApi.expenses}/${id}`, form, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200) {
                    return response.data;
                }
                throw new Error('Failed to update expense');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async delete(id: string): Promise<DeleteExpenseResponse> {
            try {
                const response = await axios.delete(`${IziApi.expenses}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });
    
                if (response.status === 200 || response.status === 204) {
                    return response.data || {};
                }
                throw new Error('Failed to delete expense');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
}
