import { DataTable } from "@/components/data-table";
import React from "react";
import { useFetchExpenses } from "../api/use-fetch-expenses";
import { Row } from "@tanstack/react-table";
import { Expense, FetchExpensesRequest, FetchExpensesResponse } from "../types/expenses";
import { expenseColumns } from "../column";

interface ExpensesTableProps {
  organizationId: string;
  onRowSelectedChanged?: (rows: Row<Expense>[]) => void,
  onDataChange?: (data?: FetchExpensesResponse) => void;
  filters?: FetchExpensesRequest;
}

export const ExpenseTable = (props: ExpensesTableProps) => {
  const [request, setRequet] = React.useState<FetchExpensesRequest>({ organization_id: props.organizationId });

  const { data: expenses , isLoading} = useFetchExpenses(request);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<Expense>[]>([]);

  React.useEffect(() => {
    props?.onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, props]);

  React.useEffect(() => {
    props?.onDataChange?.(expenses);
  }, [expenses, props]);

  React.useEffect(() => {
    // only set the changed params of the filter, the unchaged leave as is
    setRequet((prev) => ({
      ...prev,
      ...props?.filters,
    }));
  }, [props?.filters]);

  return (
    <DataTable
      columns={expenseColumns}
      data={expenses?.data ?? []}
      manualPagination={true}
      total={expenses?.total}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          console.log({ pg, message: "pagination changed" });
          setPagination(pg);
          setRequet((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
        // setRequet((prev) => ({
        //   ...prev,
        //   page: pagination.pageIndex ? (pagination.pageIndex + 1).toString() : "1",
        // }));
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};