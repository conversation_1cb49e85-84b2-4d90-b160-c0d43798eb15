"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { DottedSeparator } from "@/components/dotted-separator";
import { useCreateExpense } from "../api/use-create-expense";
import { zodResolver } from "@hookform/resolvers/zod";
import {
Form,
FormField,
FormItem,
FormLabel,
FormControl,
FormMessage
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRetrieveExpense } from "../api/use-retrieve-expense";
import { useEditExpense } from "../api/use-edit-expense";
import { useEffect } from "react";
import { createExpenseSchema } from "../schemas";
import { useExpenseFormModal } from "../hooks/use-expense-modal-form";
import { useFetchExpenseCategories } from "@/features/expense_category/api/use-fetch-expense-categories";
import { ExpenseCategory } from "@/features/expense_category/types/inventoryCategory";

interface ExpenseFormProps {
onCancel?: () => void;
defaultValues?: z.infer<typeof createExpenseSchema>;
}

export const ExpenseForm = ({ onCancel }: ExpenseFormProps) => {
    const organizationId = localStorage.getItem("current_organization") ?? undefined;
    const createExpense = useCreateExpense();
    const { close, id } = useExpenseFormModal();
    const { data: categories } = useFetchExpenseCategories({
        organization_id: organizationId,
    });
    const { data: expense } = useRetrieveExpense(id);
    const editExpense = useEditExpense(id);

    const form = useForm<z.infer<typeof createExpenseSchema>>({
        resolver: zodResolver(createExpenseSchema),
        defaultValues: {
            description: "",
            amount: "",
            organization_id: organizationId || "",
            date: "",
            expense_category_id: "none",
        }
    });


useEffect(() => {
    if (id && expense?.data) {
        const expenseData = expense.data;
        form.setValue("description", expenseData.description || "");
        form.setValue("amount", expenseData.amount || "");

        // Handle expense category - use "none" if no category is selected
        const categoryValue = expenseData.expense_category_id
            ? expenseData.expense_category_id.toString()
            : "none";
        form.setValue("expense_category_id", categoryValue);

        if (expenseData.date) {
            const dateOnly = expenseData.date.split(' ')[0];
            form.setValue("date", dateOnly);
        }
    }
}, [expense?.data, id, form]);

const onSubmit = async (values: z.infer<typeof createExpenseSchema>) => {
    const submitValues = {
        ...values,
        expense_category_id: values.expense_category_id === "none" ? "" : values.expense_category_id
    };

    if (id) {
        editExpense.mutate(
            submitValues,
            {
                onSuccess: () => {
                    toast.success("Expense updated successfully");
                    form.reset();
                    close();
                },
                onError: (error: unknown) => {
                    const message = error instanceof Error ? error.message : "An error occurred";
                    toast.error(message);
                },
            }
        );
        return;
    }

    createExpense.mutate(
        submitValues,
        {
            onSuccess: () => {
                toast.success("Expense created successfully")
                form.reset();
                close();
            },
            onError: (error) => {
                toast.error((error as Error).message)
            }
        }
    );
};

return (
    <Card className="w-full h-full border-none shadow-none">
        <CardHeader className="flex p-7">
            <CardTitle className="text-xl font-bold">
                {id ? "Edit Expense" : "Create Expense"}
            </CardTitle>
        </CardHeader>
        <div className="px-7">
            <DottedSeparator />
        </div>
        <CardContent className="p-7">
        <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} >
                        <div className="flex flex-col gap-y-4">
                            <FormField 
                                control={form.control}
                                name="date"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Date</FormLabel>
                                        <FormControl>
                                            <Input 
                                                type="date"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="expense_category_id"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Expense Category</FormLabel>
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <FormControl>
                                                <SelectTrigger className="w-full">
                                                    <SelectValue placeholder="Select a category" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="none">No Category</SelectItem>
                                                {categories?.data?.map((category: ExpenseCategory) => (
                                                    <SelectItem
                                                        key={category.id}
                                                        value={category.id.toString()}
                                                    >
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            
                            <FormField 
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter description"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="amount"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Amount</FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="Enter amount"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            


                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createExpense.isPending || editExpense.isPending}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createExpense.isPending || editExpense.isPending}
                            >
                                { id ? "Save Changes" : "Create Expense"}
                            </Button>
                        </div>
                    </form>
                </Form>
        </CardContent>
    </Card>
);
};