import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreH<PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import React from "react";
// import { useRouter } from "next/navigation";
import { Expense } from "../types/expenses";
import { useExpenseFormModal } from "../hooks/use-expense-modal-form";
import { useDeleteExpense } from "../api/use-delete-expense";
import { useConfirm } from "@/hooks/use-confirm";

type Props = {
  expense: Expense;
};

export const ExpenseTableActions = ({ expense }: Props) => {
  const { edit } = useExpenseFormModal();

  const { mutate, isPending } = useDeleteExpense();
  
    const [DeletingDialog, confirmDelete] = useConfirm(
      "Delete Expense",
      "This action cannot be undone",
      "ghost"
    );
  
    const handleDelete = async () => {
      const ok = await confirmDelete();
      if (!ok) return;
      mutate(expense.id.toString(), {
        onSuccess: () => {
          console.log("Customer deleted successfully");
        },
        onError: () => {
          console.error("Error deleting expense:", expense.id);
        },
      });
    };

  // const router = useRouter();

  return (
    <>
    <DeletingDialog />
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem
            className="flex"
            onClick={() => router.push(`expenses/${expense.id}`)}
          >
            <Eye /> View
          </DropdownMenuItem> */}
          <DropdownMenuItem onClick={() => edit(expense.id.toString())}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          >
            <Trash color="red" /> Delete{" "}
            {isPending && <Loader2 className="spin" />}
          </DropdownMenuItem>

          {/* {expense.deleted_at ? (
            <DropdownMenuItem onClick={restore}>
              <FaRecycle color="green" /> Restore{" "}
              {editOrganization.isPending && <Loader2 className="spin" />}
            </DropdownMenuItem>
          ) : (
            <div></div>
          )} */}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
    </>
  );
};