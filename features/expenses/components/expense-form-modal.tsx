"use client";

import { ResponsiveModal } from "@/components/responsive-modal";
import { ExpenseForm } from "./expense-form";
import { useExpenseFormModal } from "../hooks/use-expense-modal-form";

export const ExpenseFormModal = () => {
    const { isOpen, setIsOpen, close } = useExpenseFormModal();
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}> 
            <ExpenseForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}