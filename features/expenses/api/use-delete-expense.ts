import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Expenses } from "../server/Expense";

export const useDeleteExpense = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await Expenses.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Expense deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["expenses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete expense";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
