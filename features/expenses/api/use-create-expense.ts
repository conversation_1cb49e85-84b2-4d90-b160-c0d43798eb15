import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateExpenseRequest } from "../types/expenses";
import { Expenses } from "../server/Expense";

export const useCreateExpense = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateExpenseRequest) => {

            const response = await Expenses.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["expenses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create expense";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
