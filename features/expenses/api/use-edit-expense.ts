import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateExpenseRequest } from "../types/expenses";
import { Expenses } from "../server/Expense";

export const useEditExpense = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateExpenseRequest) => {
            const response = await Expenses.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["expenses"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit expense";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};