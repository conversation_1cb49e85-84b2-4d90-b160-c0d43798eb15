import { useQuery } from "@tanstack/react-query";
import { FetchExpensesRequest } from "../types/expenses";
import { Expenses } from "../server/Expense";

export const useFetchExpenses = (filters?: FetchExpensesRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["expenses", _filters],
    queryFn: () => Expenses.getAllExpenses({
        search: _filters.search,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
