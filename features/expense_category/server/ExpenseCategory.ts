import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { CreateExpenseCategoryRequest, CreateExpenseCategoryResponse, DeleteExpenseCategoryResponse, FetchExpenseCategoryRequest, RetrieveExpenseCategoryResponse, UpdateExpenseCategoryRequest, UpdateExpenseCategoryResponse } from "../types/inventoryCategory";

export class ExpenseCategories {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async getAllExpenseCategories(request: FetchExpenseCategoryRequest) {
        try {
            const response = await axios.get(IziApi.expenseCategory, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

    public static async create(form: CreateExpenseCategoryRequest): Promise<CreateExpenseCategoryResponse> {
            try {
                const response = await axios.post(IziApi.expenseCategory, form, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200 || response.status === 201) {
                    return response.data;
                }
                throw new Error('Failed to create expense category');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async getSingleExpenseCategory(id: string): Promise<RetrieveExpenseCategoryResponse> {
            try {
                const response = await axios.get(`${IziApi.expenseCategory}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });

                if (response.status === 200) {
                    return response;
                }
                throw new Error('Failed to retrieve expense category');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async update(form: UpdateExpenseCategoryRequest, id: string): Promise<UpdateExpenseCategoryResponse> {
            try {
                const response = await axios.put(`${IziApi.expenseCategory}/${id}`, form, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                        'Content-Type': 'application/json',
                    },
                });
    
                if (response.status === 200) {
                    return response.data;
                }
                throw new Error('Failed to update expense category');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
    
        public static async delete(id: string): Promise<DeleteExpenseCategoryResponse> {
            try {
                const response = await axios.delete(`${IziApi.expenseCategory}/${id}`, {
                    headers: {
                        Authorization: `Bearer ${this.token}`,
                    },
                });
    
                if (response.status === 200 || response.status === 204) {
                    return response.data || {};
                }
                throw new Error('Failed to delete expense category');
            } catch (error: unknown) {
                if (axios.isAxiosError(error) && error.response) {
                    throw error.response.data;
                }
                throw error;
            }
        }
}
