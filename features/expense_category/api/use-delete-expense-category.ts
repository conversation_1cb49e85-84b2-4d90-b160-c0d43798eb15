import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { ExpenseCategories } from "../server/ExpenseCategory";

export const useDeleteExpenseCategory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (id: string) => {

            const response = await ExpenseCategories.delete(id);

            return response; 
        },
        onSuccess: () => {
            toast.success("Expense Category deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to delete expense category";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
