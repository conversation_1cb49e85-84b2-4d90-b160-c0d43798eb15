import { useQuery } from "@tanstack/react-query"
import { FetchExpenseCategoryRequest } from "../types/inventoryCategory";
import { ExpenseCategories } from "../server/ExpenseCategory";

export const useFetchExpenseCategories = (filters?: FetchExpenseCategoryRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["expense-categories", _filters],
    queryFn: () => ExpenseCategories.getAllExpenseCategories({
        search: _filters.search,
        query: _filters.query,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
