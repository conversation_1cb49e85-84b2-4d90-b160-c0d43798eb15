import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CreateExpenseCategoryRequest } from "../types/inventoryCategory";
import { ExpenseCategories } from "../server/ExpenseCategory";

export const useCreateExpenseCategory = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: CreateExpenseCategoryRequest) => {

            const response = await ExpenseCategories.create(form);

            return response; 
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to create expense categories";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};
