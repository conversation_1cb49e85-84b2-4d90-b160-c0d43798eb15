import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateExpenseCategoryRequest } from "../types/inventoryCategory";
import { ExpenseCategories } from "../server/ExpenseCategory";

export const useEditExpenseCategory = (id: string = "") => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (form: UpdateExpenseCategoryRequest) => {
            const response = await ExpenseCategories.update(form,id);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["expense-categories"] });
        },
        onError: (error: unknown) => {
            const message = error instanceof Error ? error.message : "Failed to edit expense category";
            toast.error(message);
            console.error(error);
        },
    });

    return mutation;
};