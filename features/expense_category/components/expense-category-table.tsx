import React from "react";
import { Row } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import { FetchExpenseCategoryRequest, FetchExpenseCategoryResponse, ExpenseCategory } from "../types/expense_category";
import { useFetchExpenseCategories } from "../api/use-fetch-expense-categories";
import { expenseCategoryColumns } from "../column";

interface ExpenseCategoryTableProps {
  onRowSelectedChanged?: (rows: Row<ExpenseCategory>[]) => void,
  onDataChange?: (data?: FetchExpenseCategoryResponse | undefined) => void;
  filters?: FetchExpenseCategoryRequest;
}

export const ExpenseCategoryTable = (props?: ExpenseCategoryTableProps) => {
  const [request, setRequest] = React.useState<FetchExpenseCategoryRequest>(props?.filters ?? {});

  const { data: categories , isLoading} = useFetchExpenseCategories(request);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<ExpenseCategory>[]>([]);
  
  React.useEffect(() => {
    props?.onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, props]);

  React.useEffect(() => {
    props?.onDataChange?.(categories);
  }, [categories, props]);

  return (
    <DataTable
      columns={expenseCategoryColumns}
      data={categories?.data ? categories.data : []}
      manualPagination={true}
      total={categories?.total}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev: any) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  )
}