import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreH<PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import React from "react";
import { ExpenseCategory } from "../types/inventoryCategory";
import { useExpenseCategoryFormModal } from "../hooks/use-expense-category-form-modal";
import { useDeleteExpenseCategory } from "../api/use-delete-expense-category";
import { useConfirm } from "@/hooks/use-confirm";

type Props = {
  category: ExpenseCategory;
};

export const ExpenseCategoryTableActions = ({ category }: Props) => {
  const { edit } = useExpenseCategoryFormModal();

const { mutate, isPending } = useDeleteExpenseCategory();
  
    const [DeletingDialog, confirmDelete] = useConfirm(
      "Delete Expense Category",
      "This action cannot be undone",
      "ghost"
    );
  
    const handleDelete = async () => {
      const ok = await confirmDelete();
      if (!ok) return;
      mutate(category.id.toString(), {
        onSuccess: () => {
          console.log("Expense category deleted successfully");
        },
        onError: () => {
          console.error("Error deleting Expense category:", category.id);
        },
      });
    };

  return (
    <>
    <DeletingDialog />
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {/* <DropdownMenuItem className="flex" onClick={() => router.push(`/products/${category.id}?type=category`)}>
            <Eye /> View
          </DropdownMenuItem> */}
          <DropdownMenuItem
             onClick={() => edit(category.id.toString())}
            >
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          ><Trash color="red" /> Delete 
           {isPending && <Loader2 className="spin"/> }
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
    </>
  );
};