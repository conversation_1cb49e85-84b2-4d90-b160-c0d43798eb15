"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DottedSeparator } from "@/components/dotted-separator";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import { toast } from "sonner";
import { useCreateExpenseCategory } from "../api/use-create-expense-category";
import { Textarea } from "@/components/ui/textarea";
import { createExpenseCategorySchema } from "../schemas";
import { useFetchExpenseCategories } from "../api/use-fetch-expense-categories";
import { useExpenseCategoryFormModal } from "../hooks/use-expense-category-form-modal";
import { useRetrieveExpenseCategory } from "../api/use-retrieve-expense-category";
import { useEditExpenseCategory } from "../api/use-edit-expense-category";

interface CategoryFormProps {
  onCancel?: () => void;
  defaultValues?: z.infer<typeof createExpenseCategorySchema>;
}

export const ExpenseCategoryForm = ({ onCancel }: CategoryFormProps) => {
  const organizationId = localStorage.getItem('current_organization') ?? undefined ;
  const { data: categories } = useFetchExpenseCategories({ 
    organization_id: organizationId 
});

  console.log('categories', categories)

  const createExpenseCategory = useCreateExpenseCategory();
  const { close, id } = useExpenseCategoryFormModal();

  const { data: category } = useRetrieveExpenseCategory(id);
  const editExpenseCategory = useEditExpenseCategory(id);



  const form = useForm<z.infer<typeof createExpenseCategorySchema>>({
    resolver: zodResolver(createExpenseCategorySchema),
    defaultValues: {
      name: "",
      description: "",
      organization_id: organizationId,
      parent_id: "",
    },
  });


  useEffect(() => {
        if (id && category?.data) {
            const categoryData = category.data;
            form.setValue("name", categoryData.name || ""); 
            form.setValue("description", categoryData.description || ""); 
            form.setValue("parent_id", categoryData.parent_id || "");  
        }
    }, [category?.data, id, form]);

  const onSubmit = (values: z.infer<typeof createExpenseCategorySchema>) => {
    if (!!id) {
      editExpenseCategory.mutate(
        values,
        {
          onSuccess: () => {
            toast.success("Category updated successfully");
            form.reset();
            close();
          },
          onError: (error) => {
            toast.error(
              typeof error === "object" && error && "message" in error
                ? (error as { message: string }).message
                : "An error occurred"
            );
          },
        }
      );
      return;
    }
    createExpenseCategory.mutate(
      values,
      {
        onSuccess: () => {
          toast.success("Category created successfully");
          form.reset();
          close();
          // Redirect to the organization page
        },
        onError: (error) => {
          toast.error(
            typeof error === "object" && error && "message" in error
              ? (error as { message: string }).message
              : "An error occurred"
          );
        },
      }
    );
  };



  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold">
          {id ? "Edit Category" : "Create Category"}
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <DottedSeparator />
      </div>

      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category Name</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter category name" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
               <FormField
                 control={form.control}
                 name="description"
                 render={({ field }) => (
                  <FormItem>
                  <FormLabel>Description</FormLabel>
                   <FormControl>
                  <Textarea {...field} rows={4} placeholder="Enter Description" />
                   </FormControl>
                   <FormMessage />
                   </FormItem>
                   )}
                 />
              
            </div>
            <DottedSeparator className="py-7" />
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={
                  createExpenseCategory.isPending ||
                  editExpenseCategory.isPending
                }
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  createExpenseCategory.isPending ||
                  editExpenseCategory.isPending
                }
              >
                {id ? "Save Category" : "Create Category"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};