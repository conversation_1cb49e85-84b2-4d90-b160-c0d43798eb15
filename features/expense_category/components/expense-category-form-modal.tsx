"use client";

import { ResponsiveModal } from "@/components/responsive-modal";
import { ExpenseCategoryForm } from "./expense-category-form";
import { useExpenseCategoryFormModal } from "../hooks/use-expense-category-form-modal";

export const ExpenseCategoryFormModal = () => {
    const { isOpen, setIsOpen, close } = useExpenseCategoryFormModal();
    return (
        <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}> 
            <ExpenseCategoryForm onCancel={() => close()} />
        </ResponsiveModal>
    );
}