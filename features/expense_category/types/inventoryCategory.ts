import { z } from "zod";
import { createExpenseCategorySchema } from "../schemas";

export interface ExpenseCategory {
    id: number;
    name: string;
    description: string;
    organization_id: string;
    parent_id: string;

    created_at: string;
    updated_at: string;
    deleted_at: string;
}

// Fetch expense category
export type FetchExpenseCategoryRequest = {
    query?: string;
    search?: string;
    organization_id?: string;
    with_trashed?: boolean;
    page?: string;
}

export type FetchExpenseCategoryResponse = {
    data: ExpenseCategory[];
    total: number;
}

// Create expense category
export type CreateExpenseCategoryRequest = z.infer<typeof createExpenseCategorySchema>;

export interface CreateExpenseCategoryResponse {
  data: ExpenseCategory;
}

// Update Organization
export type UpdateExpenseCategoryRequest = z.infer<typeof createExpenseCategorySchema>;

// use the same response as create category
export type UpdateExpenseCategoryResponse = CreateExpenseCategoryResponse;

//retrieve
// Retrieve Organization
export interface RetrieveExpenseCategoryResponse {
    data: ExpenseCategory;
  }
  

// Delete category
export interface DeleteExpenseCategoryResponse {
    data?: ExpenseCategory;
  }