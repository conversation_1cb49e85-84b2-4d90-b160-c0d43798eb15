import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import axios from "axios";
import { toast } from "sonner";
import { CreateOrganizationFormData } from "../schemas";

export class Organization {
  
  public static async create(formData: CreateOrganizationFormData) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.post(IziApi.organizations + '/store', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 || response.status === 201) {
        toast.success("Organization created successfully");
        return response.data.data; 
      }
     } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }

  public static async getAll() {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(IziApi.getOrganizations, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        console.log(response.data.data);
        return response.data.data; 
      }
     } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }

  public static async getById(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(`${IziApi.api}/organizations/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data.data; 
      }
    } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }

  public static async update(id: number, formData: Partial<CreateOrganizationFormData>) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.put(`${IziApi.api}/organizations/${id}`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200) {
        return response.data.data; 
      }
     } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }

  public static async delete(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.delete(`${IziApi.api}/organizations/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data; 
      }
     } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }

  public static async select(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(IziApi.singleOrganization(id), {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200) {
        console.log(response.data.data);
        return response.data.data; 
      }
     } catch (error: unknown) {
            if (error && typeof error === "object" && "response" in error && error.response && typeof error.response === "object" && "data" in error.response) {
                throw error.response.data;
            }
            throw error;
        }
  }
}
