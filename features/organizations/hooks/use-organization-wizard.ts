import { useState } from "react";
import { useRouter } from "next/navigation";
import { OrganizationWizardFormData } from "../schemas";

export const useOrganizationWizard = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<OrganizationWizardFormData>>({});
  const router = useRouter();

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, 3));
  };

  const previousStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const updateFormData = (stepData: Partial<OrganizationWizardFormData>) => {
    setFormData((prev) => ({ ...prev, ...stepData }));
  };

  const submitWizard = async () => {
    try {
      // Here you would typically call an API to create the organization
      console.log("Submitting organization wizard data:", formData);
      
      // For now, just redirect to store type selection
      router.push("/setup/pick-store-type");
    } catch (error) {
      console.error("Error submitting wizard:", error);
      throw error;
    }
  };

  return {
    currentStep,
    formData,
    nextStep,
    previousStep,
    updateFormData,
    submitWizard,
    setCurrentStep,
  };
};
