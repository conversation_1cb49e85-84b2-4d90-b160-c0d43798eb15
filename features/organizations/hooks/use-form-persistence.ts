import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { OrganizationWizardFormData } from "../schemas";

const STORAGE_KEY = "organization_wizard_form_data";

export const useFormPersistence = (
  form: UseFormReturn<OrganizationWizardFormData>,
  currentStep: number
) => {
  // Save form data to localStorage whenever form values change
  useEffect(() => {
    const subscription = form.watch((data) => {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify({
          formData: data,
          currentStep,
          timestamp: Date.now(),
        }));
      } catch (error) {
        console.warn("Failed to save form data to localStorage:", error);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, currentStep]);

  // Load saved form data on component mount
  const loadSavedData = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        const parsed = JSON.parse(savedData);
        const { formData, currentStep: savedStep, timestamp } = parsed;
        
        // Check if data is not too old (24 hours)
        const isDataFresh = Date.now() - timestamp < 24 * 60 * 60 * 1000;
        
        if (isDataFresh && formData) {
          return { formData, savedStep };
        }
      }
    } catch (error) {
      console.warn("Failed to load saved form data:", error);
    }
    return null;
  };

  // Clear saved data
  const clearSavedData = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn("Failed to clear saved form data:", error);
    }
  };

  return {
    loadSavedData,
    clearSavedData,
  };
};
