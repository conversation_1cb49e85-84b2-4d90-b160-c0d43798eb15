export interface Organization {
  id: string;
  name: string;
  phone: string;
  address: string;
  description: string | null;
  featured_image?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
  members_count?: string;
  logo?: string;
  owner_id?: string;
  team_id?: string;
  country?: string;
  region?: string;
  district?: string;
  ward?: string | null;
  street?: string;
  latitude?: string;
  longitude?: string;
  tokens?: string;
  pivot?: {
    user_id: string;
    organization_id: string;
  };
  domains?: Array<{
    id: string;
    name: string;
    description: string;
    icon: string | null;
    sort_order: string;
    is_active: string;
    created_at: string;
    updated_at: string;
    pivot: {
      organization_id: string;
      domain_id: string;
    };
  }>;
}



export interface CreateOrganizationRequest {
  name: string;
  phone: string;
  address: string;
  description: string;
  featured_image?: string;
}

export interface CreateOrganizationResponse {
  data: Organization;
  message: string;
}

export interface GetOrganizationsResponse {
  data: Organization[];
  message: string;
}

export interface SelectOrganizationResponse {
  data: {
    organization: Organization;
    token?: string;
  };
  message: string;
}
