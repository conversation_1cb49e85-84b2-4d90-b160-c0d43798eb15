import { z } from "zod";

export const createOrganizationSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(100, "Name must be less than 100 characters"),
  phone: z.string().min(10, "Phone number is required").max(20, "Phone number is too long"),
  address: z.string().min(1, "Address is required").max(255, "Address must be less than 255 characters"),
  description: z.string().min(1, "Description is required").max(500, "Description must be less than 500 characters"),
  featured_image: z.string().optional(),
});

// Multi-step organization wizard schemas
export const shopDetailsSchema = z.object({
  store_name: z.string().min(1, "Store name is required").max(100, "Store name must be less than 100 characters"),
  store_phone: z.string().min(10, "Store phone number is required").max(20, "Phone number is too long"),
  shop_address: z.string().min(1, "Shop address is required").max(255, "Address must be less than 255 characters"),
});

export const ownerDetailsSchema = z.object({
  owner_name: z.string().min(1, "Owner name is required").max(100, "Owner name must be less than 100 characters"),
  owner_phone: z.string().min(10, "Owner phone number is required").max(20, "Phone number is too long"),
  owner_email: z.string().email("Invalid email address").optional().or(z.literal("")),
  same_as_logged_user: z.boolean().default(false),
});

export const storeKeeperSchema = z.object({
  keeper_name: z.string().min(1, "Store keeper name is required").max(100, "Name must be less than 100 characters"),
  keeper_phone: z.string().min(10, "Store keeper phone number is required").max(20, "Phone number is too long"),
  keeper_email: z.string().email("Invalid email address").optional().or(z.literal("")),
  same_as_logged_user: z.boolean().default(false),
});

export const organizationWizardSchema = z.object({
  shop_details: shopDetailsSchema,
  owner_details: ownerDetailsSchema,
  store_keeper: storeKeeperSchema,
});

export type CreateOrganizationFormData = z.infer<typeof createOrganizationSchema>;
export type ShopDetailsFormData = z.infer<typeof shopDetailsSchema>;
export type OwnerDetailsFormData = z.infer<typeof ownerDetailsSchema>;
export type StoreKeeperFormData = z.infer<typeof storeKeeperSchema>;
export type OrganizationWizardFormData = z.infer<typeof organizationWizardSchema>;
