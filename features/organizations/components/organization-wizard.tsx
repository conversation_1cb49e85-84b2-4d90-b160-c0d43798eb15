"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight, Store, User, UserCheck, Check, Loader2, CheckCircle } from "lucide-react";
import { useStateManager } from "@/hooks/use-context";
import { useRouter } from "next/navigation";
import { useFormPersistence } from "../hooks/use-form-persistence";
import { toast } from "sonner";

// Step components
import { ShopDetailsStep } from "./wizard-steps/shop-details-step";
import { OwnerDetailsStep } from "./wizard-steps/owner-details-step";
import { StoreKeeperStep } from "./wizard-steps/store-keeper-step";
import { SummaryStep } from "./wizard-steps/summary-step";

// Types and schemas
import {
  organizationWizardSchema,
  OrganizationWizardFormData,
  ShopDetailsFormData,
  OwnerDetailsFormData,
  StoreKeeperFormData,
} from "../schemas";

const STEPS = [
  {
    id: 1,
    title: "Shop Details",
    description: "Basic information about your store",
    icon: Store,
  },
  {
    id: 2,
    title: "Owner Details",
    description: "Information about the store owner",
    icon: User,
  },
  {
    id: 3,
    title: "Store Keeper",
    description: "Information about the store keeper",
    icon: UserCheck,
  },
  {
    id: 4,
    title: "Review",
    description: "Review your information",
    icon: CheckCircle,
  },
];

export const OrganizationWizard = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [stepValidation, setStepValidation] = useState<Record<number, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useStateManager();
  const router = useRouter();

  const form = useForm<OrganizationWizardFormData>({
    resolver: zodResolver(organizationWizardSchema),
    defaultValues: {
      shop_details: {
        store_name: "",
        store_phone: "",
        shop_address: "",
      },
      owner_details: {
        owner_name: "",
        owner_phone: "",
        owner_email: "",
        same_as_logged_user: false,
      },
      store_keeper: {
        keeper_name: "",
        keeper_phone: "",
        keeper_email: "",
        same_as_logged_user: false,
      },
    },
  });

  // Form persistence
  const { loadSavedData, clearSavedData } = useFormPersistence(form, currentStep);

  // Load saved data on mount
  useEffect(() => {
    const savedData = loadSavedData();
    if (savedData) {
      const { formData, savedStep } = savedData;

      // Restore form data
      Object.keys(formData).forEach((key) => {
        if (formData[key as keyof OrganizationWizardFormData]) {
          form.setValue(key as keyof OrganizationWizardFormData, formData[key as keyof OrganizationWizardFormData]);
        }
      });

      // Restore current step
      setCurrentStep(savedStep);
    }
  }, []);

  // Validate steps and update validation state
  useEffect(() => {
    const validateSteps = async () => {
      const validation: Record<number, boolean> = {};

      validation[1] = await form.trigger("shop_details", { shouldFocus: false });
      validation[2] = await form.trigger("owner_details", { shouldFocus: false });
      validation[3] = await form.trigger("store_keeper", { shouldFocus: false });
      validation[4] = true; // Summary step is always valid

      setStepValidation(validation);
    };

    // Debounce validation to avoid infinite loops
    const timeoutId = setTimeout(validateSteps, 300);
    return () => clearTimeout(timeoutId);
  }, [currentStep]); // Only validate when step changes

  const progress = (currentStep / STEPS.length) * 100;

  const handleNext = async () => {
    setIsLoading(true);
    let isValid = false;

    try {
      // Validate current step
      switch (currentStep) {
        case 1:
          isValid = await form.trigger("shop_details");
          break;
        case 2:
          isValid = await form.trigger("owner_details");
          break;
        case 3:
          isValid = await form.trigger("store_keeper");
          break;
        case 4:
          // Summary step doesn't need validation
          isValid = true;
          break;
      }

      if (isValid && currentStep < STEPS.length) {
        setCurrentStep(currentStep + 1);
        toast.success(`Step ${currentStep} completed successfully!`);
      } else if (!isValid) {
        toast.error("Please fix the errors before continuing");
        // Scroll to first error if validation fails
        const firstError = document.querySelector('[data-invalid="true"]');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to previous steps or current step
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
    }
  };

  const handleFinish = async () => {
    setIsLoading(true);

    try {
      const isValid = await form.trigger();
      if (isValid) {
        // Get form data
        const formData = form.getValues();
        console.log("Organization wizard completed with data:", formData);

        // Clear saved form data since we're completing the wizard
        clearSavedData();

        toast.success("Organization setup completed! Redirecting to store type selection...");

        // For now, just redirect to store type selection
        // In a real implementation, you would save the data first
        setTimeout(() => {
          router.push("/setup/pick-store-type");
        }, 1000);
      } else {
        toast.error("Please fix all validation errors before finishing");
        // Scroll to first error if validation fails
        const firstError = document.querySelector('[data-invalid="true"]');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <ShopDetailsStep />;
      case 2:
        return <OwnerDetailsStep />;
      case 3:
        return <StoreKeeperStep />;
      case 4:
        return <SummaryStep />;
      default:
        return null;
    }
  };

  const currentStepData = STEPS[currentStep - 1];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Create Your Organization
          </h1>
          <p className="text-gray-600 text-lg">
            Set up your organization step by step
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isCompleted = currentStep > step.id;
              const isCurrent = currentStep === step.id;
              const isValid = stepValidation[step.id];
              const hasError = !isValid && currentStep > step.id;

              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div
                    onClick={() => handleStepClick(step.id)}
                    className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-colors ${
                      isCompleted && isValid
                        ? "bg-green-500 text-white cursor-pointer hover:bg-green-600"
                        : hasError
                        ? "bg-red-500 text-white cursor-pointer hover:bg-red-600"
                        : isCurrent
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-500"
                    } ${step.id <= currentStep ? "cursor-pointer" : "cursor-default"}`}
                  >
                    {isCompleted && isValid ? (
                      <Check className="w-5 h-5" />
                    ) : hasError ? (
                      <span className="text-xs font-bold">!</span>
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </div>
                  <span
                    className={`text-xs font-medium ${
                      isCurrent
                        ? "text-blue-600"
                        : hasError
                        ? "text-red-600"
                        : isCompleted && isValid
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Main Card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-900">
              {currentStepData.title}
            </CardTitle>
            <p className="text-gray-600 mt-2">{currentStepData.description}</p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            <Form {...form}>
              {/* Step Content */}
              <div className="mb-8 transition-all duration-300 ease-in-out">
                {renderStepContent()}
              </div>
            </Form>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>

              {currentStep === STEPS.length ? (
                <Button
                  type="button"
                  onClick={handleFinish}
                  disabled={isLoading}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 disabled:opacity-50"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Finishing...
                    </>
                  ) : (
                    <>
                      Finish
                      <Check className="w-4 h-4" />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={isLoading}
                  className="flex items-center gap-2 disabled:opacity-50"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Validating...
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
