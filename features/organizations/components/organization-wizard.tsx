"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight, Store, User, UserCheck, Check } from "lucide-react";
import { useStateManager } from "@/hooks/use-context";
import { useRouter } from "next/navigation";

// Step components
import { ShopDetailsStep } from "./wizard-steps/shop-details-step";
import { OwnerDetailsStep } from "./wizard-steps/owner-details-step";
import { StoreKeeperStep } from "./wizard-steps/store-keeper-step";

// Types and schemas
import {
  organizationWizardSchema,
  OrganizationWizardFormData,
  ShopDetailsFormData,
  OwnerDetailsFormData,
  StoreKeeperFormData,
} from "../schemas";

const STEPS = [
  {
    id: 1,
    title: "Shop Details",
    description: "Basic information about your store",
    icon: Store,
  },
  {
    id: 2,
    title: "Owner Details",
    description: "Information about the store owner",
    icon: User,
  },
  {
    id: 3,
    title: "Store Keeper",
    description: "Information about the store keeper",
    icon: UserCheck,
  },
];

export const OrganizationWizard = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const { user } = useStateManager();
  const router = useRouter();

  const form = useForm<OrganizationWizardFormData>({
    resolver: zodResolver(organizationWizardSchema),
    defaultValues: {
      shop_details: {
        store_name: "",
        store_phone: "",
        shop_address: "",
      },
      owner_details: {
        owner_name: "",
        owner_phone: "",
        owner_email: "",
        same_as_logged_user: false,
      },
      store_keeper: {
        keeper_name: "",
        keeper_phone: "",
        keeper_email: "",
        same_as_logged_user: false,
      },
    },
  });

  const progress = (currentStep / STEPS.length) * 100;

  const handleNext = async () => {
    let isValid = false;

    // Validate current step
    switch (currentStep) {
      case 1:
        isValid = await form.trigger("shop_details");
        break;
      case 2:
        isValid = await form.trigger("owner_details");
        break;
      case 3:
        isValid = await form.trigger("store_keeper");
        break;
    }

    if (isValid && currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    } else if (!isValid) {
      // Scroll to first error if validation fails
      const firstError = document.querySelector('[data-invalid="true"]');
      if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinish = async () => {
    const isValid = await form.trigger();
    if (isValid) {
      // Get form data
      const formData = form.getValues();
      console.log("Organization wizard completed with data:", formData);

      // For now, just redirect to store type selection
      // In a real implementation, you would save the data first
      router.push("/setup/pick-store-type");
    } else {
      // Scroll to first error if validation fails
      const firstError = document.querySelector('[data-invalid="true"]');
      if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <ShopDetailsStep />;
      case 2:
        return <OwnerDetailsStep />;
      case 3:
        return <StoreKeeperStep />;
      default:
        return null;
    }
  };

  const currentStepData = STEPS[currentStep - 1];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Create Your Organization
          </h1>
          <p className="text-gray-600 text-lg">
            Set up your organization step by step
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isCompleted = currentStep > step.id;
              const isCurrent = currentStep === step.id;

              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-colors ${
                      isCompleted
                        ? "bg-green-500 text-white"
                        : isCurrent
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-500"
                    }`}
                  >
                    {isCompleted ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </div>
                  <span
                    className={`text-xs font-medium ${
                      isCurrent ? "text-blue-600" : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
              );
            })}
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Main Card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-900">
              {currentStepData.title}
            </CardTitle>
            <p className="text-gray-600 mt-2">{currentStepData.description}</p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            <Form {...form}>
              {/* Step Content */}
              <div className="mb-8 transition-all duration-300 ease-in-out">
                {renderStepContent()}
              </div>
            </Form>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>

              {currentStep === STEPS.length ? (
                <Button
                  type="button"
                  onClick={handleFinish}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                >
                  Finish
                  <Check className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleNext}
                  className="flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
