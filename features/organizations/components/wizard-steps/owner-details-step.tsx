"use client";

import React, { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { User, Phone, Mail } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { OrganizationWizardFormData } from "../../schemas";
import { User as UserType } from "@/features/auth/types/auth";
import { useStateManager } from "@/hooks/use-context";

export const OwnerDetailsStep: React.FC = () => {
  const form = useFormContext<OrganizationWizardFormData>();
  const { user } = useStateManager();
  const sameAsLoggedUser = form.watch("owner_details.same_as_logged_user");

  // Auto-fill owner details when "same as logged user" is checked
  useEffect(() => {
    if (sameAsLoggedUser && user) {
      form.setValue("owner_details.owner_name", user.name || "");
      form.setValue("owner_details.owner_phone", user.phone || "");
      form.setValue("owner_details.owner_email", user.email || "");
    } else if (!sameAsLoggedUser) {
      // Clear fields when unchecked
      form.setValue("owner_details.owner_name", "");
      form.setValue("owner_details.owner_phone", "");
      form.setValue("owner_details.owner_email", "");
    }
  }, [sameAsLoggedUser, user, form]);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <User className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900">Owner Information</h3>
        <p className="text-gray-600 text-sm">
          Provide details about the store owner
        </p>
      </div>

      <div className="space-y-4">
        {/* Same as logged user checkbox */}
        <FormField
          name="owner_details.same_as_logged_user"
          control={form.control}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-gray-50">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel className="text-sm font-medium">
                  Same as logged in user
                </FormLabel>
                <p className="text-xs text-gray-600">
                  Use your account information as the owner details
                </p>
              </div>
            </FormItem>
          )}
        />

        {/* Owner Name */}
        <FormField
          name="owner_details.owner_name"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <User className="w-4 h-4" />
                Owner Name
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter owner name"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-green-400 focus:ring-green-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Owner Phone */}
        <FormField
          name="owner_details.owner_phone"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Owner Phone
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="Enter owner phone number"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-green-400 focus:ring-green-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Owner Email */}
        <FormField
          name="owner_details.owner_email"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Owner Email (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="email"
                  placeholder="Enter owner email address"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-green-400 focus:ring-green-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
