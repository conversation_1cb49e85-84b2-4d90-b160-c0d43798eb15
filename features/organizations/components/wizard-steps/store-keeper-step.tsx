"use client";

import React, { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { <PERSON>rCheck, Phone, Mail } from "lucide-react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { OrganizationWizardFormData } from "../../schemas";
import { useStateManager } from "@/hooks/use-context";

export const StoreKeeperStep: React.FC = () => {
  const form = useFormContext<OrganizationWizardFormData>();
  const { user } = useStateManager();
  const sameAsLoggedUser = form.watch("store_keeper.same_as_logged_user");

  // Auto-fill store keeper details when "same as logged user" is checked
  useEffect(() => {
    if (sameAsLoggedUser && user) {
      form.setValue("store_keeper.keeper_name", user.name || "");
      form.setValue("store_keeper.keeper_phone", user.phone || "");
      form.setValue("store_keeper.keeper_email", user.email || "");
    } else if (!sameAsLoggedUser) {
      // Clear fields when unchecked
      form.setValue("store_keeper.keeper_name", "");
      form.setValue("store_keeper.keeper_phone", "");
      form.setValue("store_keeper.keeper_email", "");
    }
  }, [sameAsLoggedUser, user, form]);
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <UserCheck className="w-8 h-8 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900">Store Keeper Information</h3>
        <p className="text-gray-600 text-sm">
          Provide details about the store keeper
        </p>
      </div>

      <div className="space-y-4">
        {/* Same as logged user checkbox */}
        <FormField
          name="store_keeper.same_as_logged_user"
          control={form.control}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 bg-gray-50">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel className="text-sm font-medium">
                  Same as logged in user
                </FormLabel>
                <p className="text-xs text-gray-600">
                  Use your account information as the store keeper details
                </p>
              </div>
            </FormItem>
          )}
        />

        {/* Store Keeper Name */}
        <FormField
          name="store_keeper.keeper_name"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <UserCheck className="w-4 h-4" />
                Store Keeper Name
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter store keeper name"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Store Keeper Phone */}
        <FormField
          name="store_keeper.keeper_phone"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Store Keeper Phone
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="Enter store keeper phone number"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Store Keeper Email */}
        <FormField
          name="store_keeper.keeper_email"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Store Keeper Email (Optional)
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="email"
                  placeholder="Enter store keeper email address"
                  disabled={sameAsLoggedUser}
                  className={`h-12 border-gray-200 focus:border-purple-400 focus:ring-purple-400/20 bg-white ${
                    sameAsLoggedUser ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <UserCheck className="w-3 h-3 text-white" />
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">
              About Store Keeper
            </h4>
            <p className="text-xs text-blue-700">
              The store keeper will be responsible for day-to-day operations, 
              inventory management, and customer service in your store.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
