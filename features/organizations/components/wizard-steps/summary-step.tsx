"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { Store, User, UserCheck, CheckCircle } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { OrganizationWizardFormData } from "../../schemas";

export const SummaryStep: React.FC = () => {
  const form = useFormContext<OrganizationWizardFormData>();
  const formData = form.getValues();

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900">Review Your Information</h3>
        <p className="text-gray-600 text-sm">
          Please review all the details before completing the setup
        </p>
      </div>

      <div className="space-y-4">
        {/* Shop Details Summary */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Store className="w-5 h-5 text-blue-600" />
              Shop Details
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium text-gray-600">Store Name:</span>
                <p className="text-sm text-gray-900">{formData.shop_details?.store_name || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Phone Number:</span>
                <p className="text-sm text-gray-900">{formData.shop_details?.store_phone || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Address:</span>
                <p className="text-sm text-gray-900">{formData.shop_details?.shop_address || "-"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Owner Details Summary */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <User className="w-5 h-5 text-green-600" />
              Owner Details
              {formData.owner_details?.same_as_logged_user && (
                <Badge variant="secondary" className="text-xs">
                  Same as logged user
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium text-gray-600">Name:</span>
                <p className="text-sm text-gray-900">{formData.owner_details?.owner_name || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Phone Number:</span>
                <p className="text-sm text-gray-900">{formData.owner_details?.owner_phone || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Email:</span>
                <p className="text-sm text-gray-900">{formData.owner_details?.owner_email || "-"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Store Keeper Details Summary */}
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <UserCheck className="w-5 h-5 text-purple-600" />
              Store Keeper Details
              {formData.store_keeper?.same_as_logged_user && (
                <Badge variant="secondary" className="text-xs">
                  Same as logged user
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <span className="text-sm font-medium text-gray-600">Name:</span>
                <p className="text-sm text-gray-900">{formData.store_keeper?.keeper_name || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Phone Number:</span>
                <p className="text-sm text-gray-900">{formData.store_keeper?.keeper_phone || "-"}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-600">Email:</span>
                <p className="text-sm text-gray-900">{formData.store_keeper?.keeper_email || "-"}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
        <div className="flex items-start gap-3">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <CheckCircle className="w-3 h-3 text-white" />
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">
              Ready to Continue
            </h4>
            <p className="text-xs text-blue-700">
              Once you click "Finish", you'll be taken to select your store type. 
              You can always update this information later from your organization settings.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
