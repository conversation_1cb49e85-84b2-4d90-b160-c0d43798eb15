"use client";

import React from "react";
import { UseFormReturn } from "react-hook-form";
import { Store, Phone, MapPin } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { OrganizationWizardFormData } from "../../schemas";

interface ShopDetailsStepProps {
  form: UseFormReturn<OrganizationWizardFormData>;
}

export const ShopDetailsStep: React.FC<ShopDetailsStepProps> = ({ form }) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Store className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900">Shop Information</h3>
        <p className="text-gray-600 text-sm">
          Tell us about your store's basic details
        </p>
      </div>

      <div className="space-y-4">
        {/* Store Name */}
        <FormField
          name="shop_details.store_name"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Store className="w-4 h-4" />
                Store Name
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter your store name"
                  className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Store Phone Number */}
        <FormField
          name="shop_details.store_phone"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Store Phone Number
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="Enter store phone number"
                  className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Shop Address */}
        <FormField
          name="shop_details.shop_address"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Shop Address
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter your shop address"
                  className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
