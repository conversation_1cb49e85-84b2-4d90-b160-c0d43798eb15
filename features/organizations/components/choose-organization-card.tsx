"use client"

import { Building2, <PERSON>, ArrowRight, Plus, MapPin, Phone, Coins, Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useStateManager } from "@/hooks/use-context";
import { useSelectOrganization } from "../api/use-select-organization";
import { motion } from "framer-motion";

// Custom colors
const COLORS = {
  primary: '#47b37c',
  primaryLight: '#c8ffd9',
  primaryDark: '#133330',
  accent: '#47b37c',
};

export const ChooseOrganizationCard = () => {
  const selectOrganization = useSelectOrganization();
  const { isLoading } = useStateManager();

  const handleSelectOrganization = (orgId: number) => {
    selectOrganization.mutate(orgId);
  };

  const organizationsListRaw = typeof window !== "undefined" ? localStorage.getItem("organizations") : null;
  let organizationsList: any[] = [];
  if (organizationsListRaw) {
    try {
      organizationsList = JSON.parse(organizationsListRaw);
    } catch {
      organizationsList = [];
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div 
            className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4"
            style={{ borderColor: COLORS.primary }}
          ></div>
          <p className="text-muted-foreground">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <Card className="border-none bg-white/95 backdrop-blur-sm overflow-hidden">
        <CardHeader
          className="p-6"
          style={{
        background: COLORS.primaryDark,
          }}
        >
          <CardTitle className="text-white text-2xl font-bold">
        Select Your Organization
          </CardTitle>
          <p className="text-white/90">
        Choose an organization to continue to your dashboard
          </p>
        </CardHeader>

        <CardContent className="p-6 md:p-8">
          <div className="grid gap-4 md:gap-6">
            {organizationsList && organizationsList.length > 0 ? (
              organizationsList.map((org: any) => (
                <motion.div
                  key={org.id}
                  whileHover={{ y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleSelectOrganization(org.id)}
                  className={`
                    relative p-6 rounded-xl border border-gray-200 cursor-pointer 
                    transition-all duration-200 hover:border-[var(--primary)] hover:shadow-md
                    bg-white group overflow-hidden
                  `}
                  style={{
                    '--primary': COLORS.primary,
                    '--primary-light': COLORS.primaryLight,
                  } as React.CSSProperties}
                >
                  {/* Glow effect on hover */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[var(--primary-light)]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4 flex-1">
                        {/* Organization Icon */}
                        <div 
                          className="w-16 h-16 bg-gradient-to-br from-[var(--primary-light)] to-[var(--primary)] rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm"
                        >
                          {org.featured_image && org.featured_image !== "/storage/" ? (
                            <img
                              src={org.featured_image}
                              alt={org.name}
                              className="w-full h-full object-cover rounded-xl"
                            />
                          ) : (
                            <Building2 className="w-8 h-8 text-white" />
                          )}
                        </div>

                        {/* Organization Details */}
                        <div className="flex-1 min-w-0">
                          {/* Name */}
                          <h3 className="font-bold text-gray-900 text-xl mb-2 group-hover:text-[var(--primary-dark)] transition-colors">
                            {org.name}
                          </h3>

                          {/* Description */}
                          {org.description && (
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                              {org.description}
                            </p>
                          )}

                          {/* Details */}
                          <div className="space-y-2">
                            {/* Location */}
                            {org.address && (
                              <div className="flex items-center text-sm text-gray-600">
                                <MapPin className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                                <span className="truncate">{org.address}</span>
                              </div>
                            )}

                            {/* Members */}
                            <div className="flex items-center text-sm text-gray-600">
                              <Users className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                              <span>{org.memberCount || 0} members</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Arrow */}
                      <div className="flex items-center ml-4">
                        <ArrowRight className="w-6 h-6 text-gray-400 group-hover:text-[var(--primary)] transition-colors" />
                      </div>
                    </div>

                    {/* Phone and Tokens Row */}
                    <div className="flex flex-wrap items-center gap-4 pt-3 border-t border-gray-100 mt-4">
                      {/* Phone */}
                      {org.phone && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                          <span>{org.phone}</span>
                        </div>
                      )}

                      {/* Tokens */}
                      <div className="flex items-center text-sm">
                        <Coins className="w-4 h-4 mr-2 text-yellow-500 flex-shrink-0" />
                        <span className="font-medium text-gray-900">
                          {org.tokens?.toLocaleString() || 0} tokens
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <Building2 className="w-10 h-10 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Organizations Found</h3>
                <p className="text-gray-500 mb-6">
                  You don&apos;t have access to any organizations yet
                </p>
                <Button
                  className="gap-2"
                  style={{
                    background: COLORS.primary,
                    color: 'white'
                  }}
                >
                  <Plus className="w-4 h-4" />
                  Create New Organization
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Create New Organization Button */}
      {organizationsList && organizationsList.length > 0 && (
        <div className="text-center">
          <Button
            variant="outline"
            className="gap-2 border-[var(--primary)] text-[var(--primary-dark)] hover:bg-[var(--primary-light)]"
            style={{
              '--primary': COLORS.primary,
              '--primary-light': COLORS.primaryLight,
              '--primary-dark': COLORS.primaryDark,
            } as React.CSSProperties}
          >
            <Plus className="w-4 h-4" />
            Add New Organization
          </Button>
        </div>
      )}
    </div>
  );
};