"use client";

import React, { useEffect, useState } from 'react';
import { TrendingUp, DollarSign, CreditCard, RefreshCw, Calendar } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarUI } from "@/components/ui/calendar";
import { format, subDays } from "date-fns";
import { FetchSalesReportRequest } from '@/features/reports/types/report';
import { useFetchSalesReport } from '@/features/reports/api/use-fetch-sales-report';
import { Skeleton } from "@/components/ui/skeleton";

const SalesReport = () => {
  const [filters, setFilters] = useState<FetchSalesReportRequest>(() => {
    const organizationId = typeof window !== 'undefined' ? localStorage.getItem("current_organization") || '' : '';
    return {
      organization_id: organizationId,
      start_date: subDays(new Date(), 7).toISOString(),
      end_date: new Date().toISOString(),
    };
  });

  const { data, isLoading, error } = useFetchSalesReport(filters);
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date(),
  });

  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      start_date: dateRange.from.toISOString(),
      end_date: dateRange.to.toISOString(),
    }));
  }, [dateRange]);

  const formatCurrency = (value: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'TSH',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(Number(value));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate max value for chart scaling
  const maxDailyValue = data?.data.daily_sales 
    ? Math.max(...data.data.daily_sales.map((day: { daily_total: string }) => Number(day.daily_total))) 
    : 0;

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="Sales Report"
            breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Sales Report" },
        ]}
          description="Loading your sales performance data..."
          icon={TrendingUp}
        />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="border-0 shadow-sm">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-1/3" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-2/3 mb-2" />
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error || !data) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="Sales Report"
            breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Sales Report" },
        ]}
          description="There was an error loading the sales report"
          icon={TrendingUp}
        />
        <Card className="border-0 shadow-sm bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">Error: {error?.message || "Failed to load data"}</p>
            <Button 
              className="mt-4"
              style={{ backgroundColor: '#47b37c', color: 'white' }}
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Sales Report"
          breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Sales Report" },
        ]}
        description={`Analyze your sales performance`}
        icon={TrendingUp}
      />
      
      {/* Date Range Picker */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className="w-[280px] justify-start text-left font-normal"
                style={{ borderColor: '#47b37c' }}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarUI
                mode="range"
                selected={dateRange}
                onSelect={(range) => {
                  if (range && range.from && range.to) {
                    setDateRange({ from: range.from, to: range.to });
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Button 
            style={{ backgroundColor: '#47b37c', color: 'white' }}
            onClick={() => console.log('Fetch data for:', dateRange)}
          >
            Apply
          </Button>
        </div>
        {data.data.generated_at && (
          <div className="text-sm text-muted-foreground">
            Report generated: {formatDate(data.data.generated_at)}
          </div>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Sales
            </CardTitle>
            <DollarSign className="h-4 w-4" style={{ color: '#47b37c' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.data.summary.total_amount)}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {data.data.summary.total_sales} transactions
              </p>
              <Badge variant="outline" className="text-xs" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                {data.data.summary.cash_sales_count} cash
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Amount Received
            </CardTitle>
            <CreditCard className="h-4 w-4" style={{ color: '#47b37c' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.data.summary.total_received)}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {formatCurrency(data.data.summary.total_returned)} returned
              </p>
              <Badge variant="outline" className="text-xs" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                +{formatCurrency(Number(data.data.summary.total_received) - Number(data.data.summary.total_returned))}
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Profit
            </CardTitle>
            <TrendingUp className="h-4 w-4" style={{ color: '#47b37c' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold" style={{ color: '#47b37c' }}>
              {formatCurrency(data.data.profit_data.total_profit)}
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                from all sales
              </p>
              <Badge variant="outline" className="text-xs" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                {Math.round((data.data.profit_data.total_profit / Number(data.data.summary.total_amount)) * 100)}% margin
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Credit Sales
            </CardTitle>
            <RefreshCw className="h-4 w-4" style={{ color: '#47b37c' }} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.data.summary.total_credit)}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {data.data.summary.credit_sales_count} credit transactions
              </p>
              <Badge variant="outline" className="text-xs" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                {Math.round((Number(data.data.summary.total_credit) / Number(data.data.summary.total_amount)) * 100)}% of total
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Sales Trend Visualization */}
      <Card className="border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Sales Trend</CardTitle>
            <div className="flex gap-2">
              <Badge variant="outline" style={{ backgroundColor: '#e8f5ee', color: '#133330' }}>
                Total: {formatCurrency(data.data.summary.total_amount)}
              </Badge>
              <Badge variant="outline" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                Days: {data.data.daily_sales.length}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <div className="flex h-full">
              {/* Y-axis labels */}
              <div className="flex flex-col justify-between pr-2 pb-4 text-xs text-muted-foreground">
                {[100, 75, 50, 25, 0].map((percent) => (
                  <div key={percent}>
                    {formatCurrency((maxDailyValue * percent) / 100)}
                  </div>
                ))}
              </div>
              
              {/* Chart area */}
              <div className="flex-1 flex flex-col">
                <div className="flex-1 relative">
                  {/* Grid lines */}
                  <div className="absolute inset-0 flex flex-col justify-between">
                    {[0, 1, 2, 3, 4].map((i) => (
                      <div key={i} className="h-px w-full bg-gray-200"></div>
                    ))}
                  </div>
                  
                  {/* Bars */}
                  <div className="absolute bottom-0 left-0 right-0 h-full flex items-end gap-2 px-2">
                    {data.data.daily_sales.map((day: { sale_date: string; daily_total: string }) => {
                      const heightPercent = (Number(day.daily_total) / maxDailyValue) * 100;
                      return (
                        <div 
                          key={day.sale_date}
                          className="flex-1 flex flex-col items-center"
                          style={{ height: '100%' }}
                        >
                          <div 
                            className="w-full rounded-t-sm hover:opacity-90 transition-opacity"
                            style={{
                              height: `${heightPercent}%`,
                              backgroundColor: '#47b37c',
                              backgroundImage: 'linear-gradient(to top, #47b37c, #6fd792)',
                            }}
                            title={`${formatDate(day.sale_date)}: ${formatCurrency(day.daily_total)}`}
                          ></div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                
                {/* X-axis labels */}
                <div className="h-8 flex justify-between px-2 text-xs text-muted-foreground overflow-hidden">
                  {data.data.daily_sales.map((day: { sale_date: string; daily_total: string }) => (
                    <div 
                      key={day.sale_date}
                      className="flex-1 text-center truncate"
                      title={formatDate(day.sale_date)}
                    >
                      {new Date(day.sale_date).getDate()}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Legend */}
            <div className="mt-4 flex justify-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-sm" style={{ backgroundColor: '#47b37c' }}></div>
                <span className="text-xs">Daily Sales</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Tables Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader>
            <CardTitle>Daily Sales</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[120px]">Date</TableHead>
                  <TableHead className="text-right">Transactions</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                  <TableHead className="text-right">Received</TableHead>
                  <TableHead className="text-right">Credit</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.daily_sales.map((day: {
                  sale_date: string;
                  sales_count: number;
                  daily_total: string;
                  daily_received: string;
                  daily_credit: string;
                }) => (
                  <TableRow key={day.sale_date} className="hover:bg-[#e8f5ee]">
                    <TableCell className="font-medium">{formatDate(day.sale_date)}</TableCell>
                    <TableCell className="text-right">{day.sales_count}</TableCell>
                    <TableCell className="text-right">{formatCurrency(day.daily_total)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(day.daily_received)}</TableCell>
                    <TableCell className="text-right">
                      {day.daily_credit !== "0.00" ? (
                        <span style={{ color: '#47b37c' }}>{formatCurrency(day.daily_credit)}</span>
                      ) : (
                        formatCurrency(day.daily_credit)
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card className="col-span-3 border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader>
            <CardTitle>Top Selling Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead className="text-right">Qty</TableHead>
                  <TableHead className="text-right">Revenue</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.top_items.map((item: { item_id: string; item_name: string; total_quantity: number; total_sales_amount: string }) => (
                  <TableRow key={item.item_id} className="hover:bg-[#e8f5ee]">
                    <TableCell className="font-medium">{item.item_name}</TableCell>
                    <TableCell className="text-right">{item.total_quantity}</TableCell>
                    <TableCell className="text-right" style={{ color: '#47b37c' }}>
                      {formatCurrency(item.total_sales_amount)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Additional Data Tables */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-3 border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader>
            <CardTitle>Top Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead className="text-right">Visits</TableHead>
                  <TableHead className="text-right">Spent</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.top_customers.map((customer: { customer_id: string; customer_name: string; total_purchases: number; total_spent: string; total_credit: string }) => (
                  <TableRow key={customer.customer_id} className="hover:bg-[#e8f5ee]">
                    <TableCell className="font-medium">{customer.customer_name}</TableCell>
                    <TableCell className="text-right">{customer.total_purchases}</TableCell>
                    <TableCell className="text-right" style={{ color: '#47b37c' }}>
                      <div className="flex items-center justify-end">
                        {formatCurrency(customer.total_spent)}
                        {customer.total_credit !== "0.00" && (
                          <Badge variant="outline" className="ml-2 text-xs" style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                            Credit
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card className="col-span-4 border-0 shadow-sm" style={{ backgroundColor: '#f8faf9' }}>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead className="text-right">Date</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Profit</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.profit_data.sales.map((sale: { 
                  id: string; 
                  code: string; 
                  customer_name: string; 
                  date: string; 
                  total_amount: string | number; 
                  profit: string | number; 
                }) => (
                  <TableRow key={sale.id} className="hover:bg-[#e8f5ee]">
                    <TableCell className="font-medium">{sale.code}</TableCell>
                    <TableCell>{sale.customer_name}</TableCell>
                    <TableCell className="text-right">{formatDate(sale.date)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(sale.total_amount)}</TableCell>
                    <TableCell className="text-right" style={{ color: '#47b37c' }}>
                      {formatCurrency(sale.profit)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Export Button */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          style={{ borderColor: '#47b37c', color: '#47b37c' }}
          className="hover:bg-[#e8f5ee]"
          onClick={() => {
            if (typeof window !== "undefined") {
              window.print();
            }
          }}
        >
          Export as PDF
        </Button>
      </div>
    </div>
  )
}

export default SalesReport;