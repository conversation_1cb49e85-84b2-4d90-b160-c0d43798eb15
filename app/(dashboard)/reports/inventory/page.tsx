"use client";

import React, { useState, useRef } from 'react';
import { 
  Package, DollarSign, TrendingUp, Pie<PERSON>hart, AlertTriangle, 
  ChevronLeft, ChevronRight, Filter, RefreshCw
} from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { Progress } from "@/components/ui/progress";


import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, 
  PieChart as RePieChart, Pie, Cell 
} from 'recharts';
import { useFetchStockReport } from '@/features/reports/api/use-fetch-stock-report';
import { StockItem, UnitType } from '@/features/reports/types/report';




const InventoryStockReport = () => {
  const getOrganizationId = () => {
    try {
      const organizationIdString = localStorage.getItem("current_organization");
      return organizationIdString ? Number(organizationIdString) : undefined;
    } catch (error) {
      console.error("Error accessing localStorage:", error);
      return undefined;
    }
  };

  const organizationId = getOrganizationId();

  const { data, isLoading } = useFetchStockReport({
    organization_id: organizationId !== undefined ? String(organizationId) : "",
  });
  
  const [page, setPage] = useState(1);
  const [tab, setTab] = useState("overview");
  const [stockFilter, setStockFilter] = useState("all");
  const reportRef = useRef<HTMLDivElement>(null);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'TSH',
      minimumFractionDigits: 2
    }).format(value);
  };

  const stockDistributionData = data ? [
    { name: 'Low Stock (≤20)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock <= 20).length },
    { name: 'Medium Stock (21-100)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 20 && item.total_stock <= 100).length },
    { name: 'Good Stock (>100)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 100).length },
  ] : [];


  const topItemsData = data ? 
    [...data.items_stock_level.data]
      .sort((a: StockItem, b: StockItem) => b.total_stock - a.total_stock)
      .slice(0, 5)
      .map((item: StockItem) => ({ name: item.item_name, value: item.total_stock }))
    : [];

  const COLORS = ['#FF8042', '#FFBB28', '#00C49F'];

  const profitMarginPercentage = data ? 
    Math.round((data.summary.expected_profit / data.summary.expected_revenue) * 100) : 0;

 

  const getStockLevelClass = (level: number) => {
    if (level <= 20) return "bg-red-100 text-red-800";
    if (level <= 100) return "bg-yellow-100 text-yellow-800";
    return "bg-green-100 text-green-800";
  };

  const filteredItems = () => {
    if (!data) return [];
    
    switch(stockFilter) {
      case "low":
        return data.items_stock_level.data.filter((item: StockItem) => item.total_stock <= 20);
      case "medium":
        return data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 20 && item.total_stock <= 100);
      case "high":
        return data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 100);
      default:
        return data.items_stock_level.data;
    }
  };


  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="flex flex-col items-center">
          <RefreshCw className="h-8 w-8 animate-spin" style={{ color: '#47b37c' }} />
          <p className="mt-4 text-lg font-medium" style={{ color: '#133330' }}>Loading inventory data...</p>
        </div>
      </div>
    );
  }


  if (!data) {
    return (
      <div className="flex items-center justify-center h-[600px]">
        <div className="flex flex-col items-center">
          <AlertTriangle className="h-8 w-8 text-red-500" />
          <p className="mt-4 text-lg font-medium text-red-600">Failed to load inventory data</p>
          <Button 
            onClick={() => window.location.reload()}
            className="mt-4" 
            style={{ backgroundColor: '#47b37c', color: 'white' }}
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Report Header */}
      <PageHeader 
         breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Inventory Stock Report" },
        ]}
        title="Inventory Stock Report"
        description="Monitor inventory levels, value, and potential revenue"
        icon={Package}
      />


      
      {/* Main Content */}
      <div ref={reportRef} className="space-y-8 print:p-4">
        {/* Tabs Navigation WITH TabsContent inside the Tabs component */}
        <Tabs value={tab} onValueChange={setTab} className="space-y-8">
          <TabsList className="grid grid-cols-3 w-full md:w-[400px] print:hidden" style={{ backgroundColor: '#e6f7ef' }}>
            <TabsTrigger 
              value="overview" 
              style={{ 
                color: tab === 'overview' ? 'white' : '#133330',
                backgroundColor: tab === 'overview' ? '#47b37c' : 'transparent'
              }}
            >
              Overview
            </TabsTrigger>
            <TabsTrigger 
              value="details" 
              style={{ 
                color: tab === 'details' ? 'white' : '#133330',
                backgroundColor: tab === 'details' ? '#47b37c' : 'transparent'
              }}
            >
              Details
            </TabsTrigger>
            <TabsTrigger 
              value="analytics" 
              style={{ 
                color: tab === 'analytics' ? 'white' : '#133330',
                backgroundColor: tab === 'analytics' ? '#47b37c' : 'transparent'
              }}
            >
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab Content */}
          <TabsContent value="overview" className="space-y-6 print:block">
            {/* Summary Cards */}
            <h2 className="text-2xl font-bold" style={{ color: '#133330' }}>Inventory Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="border-l-4 shadow-sm hover:shadow-md transition-shadow" style={{ borderLeftColor: '#47b37c' }}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Total Items</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold" style={{ color: '#133330' }}>
                      {data.summary.total_items}
                    </div>
                    <div className="p-2 rounded-full" style={{ backgroundColor: '#c8ffd9' }}>
                      <Package className="h-5 w-5" style={{ color: '#47b37c' }} />
                    </div>
                  </div>
                  <p className="text-xs mt-1 text-muted-foreground">
                    Total unique items in inventory
                  </p>
                </CardContent>
                <CardFooter className="pt-0 pb-2">
                  <div className="w-full">
                    <div className="text-xs text-muted-foreground mb-1">Stocking Rate</div>
                    <Progress value={75} className="h-1" style={{ backgroundColor: '#e6f7ef' }}>
                      <div className="h-full" style={{ backgroundColor: '#47b37c' }}></div>
                    </Progress>
                  </div>
                </CardFooter>
              </Card>

              <Card className="border-l-4 shadow-sm hover:shadow-md transition-shadow" style={{ borderLeftColor: '#47b37c' }}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Stock Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold" style={{ color: '#133330' }}>
                      {formatCurrency(data.summary.total_stock_value)}
                    </div>
                    <div className="p-2 rounded-full" style={{ backgroundColor: '#c8ffd9' }}>
                      <DollarSign className="h-5 w-5" style={{ color: '#47b37c' }} />
                    </div>
                  </div>
                  <p className="text-xs mt-1 text-muted-foreground">
                    Total inventory value at cost
                  </p>
                </CardContent>
                <CardFooter className="pt-0 pb-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Value per Item (avg)</span>
                    <span className="font-medium" style={{ color: '#133330' }}>
                      {formatCurrency(data.summary.total_stock_value / data.summary.total_items)}
                    </span>
                  </div>
                </CardFooter>
              </Card>

              <Card className="border-l-4 shadow-sm hover:shadow-md transition-shadow" style={{ borderLeftColor: '#47b37c' }}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Expected Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold" style={{ color: '#133330' }}>
                      {formatCurrency(data.summary.expected_revenue)}
                    </div>
                    <div className="p-2 rounded-full" style={{ backgroundColor: '#c8ffd9' }}>
                      <TrendingUp className="h-5 w-5" style={{ color: '#47b37c' }} />
                    </div>
                  </div>
                  <p className="text-xs mt-1 text-muted-foreground">
                    Potential revenue from current stock
                  </p>
                </CardContent>
                <CardFooter className="pt-0 pb-2">
                  <div className="flex items-center text-xs">
                    <span className="text-muted-foreground mr-2">Revenue vs. Cost Ratio:</span>
                    <Badge style={{ backgroundColor: '#c8ffd9', color: '#133330' }}>
                      {(data.summary.expected_revenue / data.summary.total_stock_value).toFixed(2)}x
                    </Badge>
                  </div>
                </CardFooter>
              </Card>

              <Card className="border-l-4 shadow-sm hover:shadow-md transition-shadow" style={{ borderLeftColor: '#47b37c' }}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">Expected Profit</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold" style={{ color: '#133330' }}>
                      {formatCurrency(data.summary.expected_profit)}
                    </div>
                    <div className="p-2 rounded-full" style={{ backgroundColor: '#c8ffd9' }}>
                      <PieChart className="h-5 w-5" style={{ color: '#47b37c' }} />
                    </div>
                  </div>
                  <p className="text-xs mt-1 text-muted-foreground">
                    Potential profit from current stock
                  </p>
                </CardContent>
                <CardFooter className="pt-0 pb-2">
                  <div className="w-full">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-muted-foreground">Profit Margin</span>
                      <span className="font-medium" style={{ color: '#47b37c' }}>{profitMarginPercentage}%</span>
                    </div>
                    <Progress value={profitMarginPercentage} className="h-1" style={{ backgroundColor: '#e6f7ef' }}>
                      <div className="h-full" style={{ backgroundColor: '#47b37c' }}></div>
                    </Progress>
                  </div>
                </CardFooter>
              </Card>
            </div>

            {/* Stock Distribution Chart */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle style={{ color: '#133330' }}>Stock Level Distribution</CardTitle>
                  <CardDescription>Breakdown of inventory by stock level</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RePieChart>
                        <Pie
                          data={stockDistributionData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {stockDistributionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} items`, 'Quantity']} />
                        <Legend />
                      </RePieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle style={{ color: '#133330' }}>Top Items by Quantity</CardTitle>
                  <CardDescription>Items with highest stock levels</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={topItemsData}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis dataKey="name" type="category" width={150} tick={{ fontSize: 12 }} />
                        <Tooltip />
                        <Bar dataKey="value" name="Stock Quantity" fill="#47b37c" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Low Stock Alert */}
            <Card style={{ backgroundColor: '#fff0f0', border: '1px solid #ffcccc' }}>
              <CardHeader>
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                  <CardTitle className="text-red-800">Low Stock Alert</CardTitle>
                </div>
                <CardDescription className="text-red-700">Items that need immediate attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {data.items_stock_level.data
                    .filter((item: StockItem) => item.total_stock <= 20)
                    .map((item: StockItem, index: number) => (
                      <div 
                        key={index} 
                        className="p-4 rounded-lg flex justify-between items-center shadow-sm hover:shadow-md transition-shadow" 
                        style={{ backgroundColor: 'white', border: '1px solid #ffcccc' }}
                      >
                        <div>
                          <div className="font-medium" style={{ color: '#133330' }}>{item.item_name}</div>
                          <div className="text-sm text-muted-foreground">SKU: {item.sku}</div>
                        </div>
                        <Badge className="bg-red-100 text-red-800">
                          {item.total_stock} units
                        </Badge>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Details Tab Content */}
          <TabsContent value="details" className="space-y-6 print:block">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
              <h2 className="text-2xl font-bold" style={{ color: '#133330' }}>Inventory Details</h2>
              
              <div className="flex items-center space-x-2 mt-3 md:mt-0">
                <Select value={stockFilter} onValueChange={setStockFilter}>
                  <SelectTrigger className="w-[180px]" style={{ borderColor: '#47b37c' }}>
                    <SelectValue placeholder="Filter by stock level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Items</SelectItem>
                    <SelectItem value="low">Low Stock (≤20)</SelectItem>
                    <SelectItem value="medium">Medium Stock (21-100)</SelectItem>
                    <SelectItem value="high">High Stock ({'>'}100)</SelectItem>
                  </SelectContent>
                </Select>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" style={{ borderColor: '#47b37c' }}>
                      <Filter className="h-4 w-4" style={{ color: '#133330' }} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Sort by Name</DropdownMenuItem>
                    <DropdownMenuItem>Sort by Stock (High to Low)</DropdownMenuItem>
                    <DropdownMenuItem>Sort by Stock (Low to High)</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Inventory Table */}
            <Card>
              <CardContent className="p-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader style={{ backgroundColor: '#133330' }}>
                      <TableRow>
                        <TableHead className="w-[50%] text-white">Item Name</TableHead>
                        <TableHead className="text-white">SKU</TableHead>
                        <TableHead className="text-white">Stock Level</TableHead>
                        <TableHead className="text-right text-white">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredItems().map((item: StockItem, index: number) => (
                        <TableRow key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50 hover:bg-gray-100'}>
                          <TableCell className="font-medium" style={{ color: '#133330' }}>{item.item_name}</TableCell>
                          <TableCell className="text-muted-foreground">{item.sku}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <span className="mr-2">{item.total_stock}</span>
                              <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                  className="h-full" 
                                  style={{ 
                                    width: `${Math.min(100, (item.total_stock / 400) * 100)}%`,
                                    backgroundColor: item.total_stock <= 20 ? '#f87171' : item.total_stock <= 100 ? '#facc15' : '#4ade80'
                                  }}
                                ></div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Badge className={getStockLevelClass(item.total_stock)}>
                              {item.total_stock <= 20 ? 'Low Stock' : item.total_stock <= 100 ? 'Medium Stock' : 'Good Stock'}
                            </Badge>
                            {item.reorder_level && item.total_stock < item.reorder_level && (
                              <Badge className="ml-2 bg-red-100 text-red-800">
                                Below Reorder
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex items-center justify-between pt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredItems().length} of {data.items_stock_level.total} items
                </div>
                
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationLink 
                        onClick={page === 1 ? undefined : () => setPage(Math.max(1, page - 1))}
                        style={{ 
                          color: page === 1 ? '#ccc' : '#47b37c',
                          pointerEvents: page === 1 ? 'none' : 'auto',
                          opacity: page === 1 ? 0.6 : 1
                        }}
                        aria-disabled={page === 1}
                      >
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        Previous
                      </PaginationLink>
                    </PaginationItem>
                    
                    <PaginationItem>
                      <PaginationLink 
                        isActive={page === 1}
                        onClick={() => setPage(1)}
                        style={{ 
                          backgroundColor: page === 1 ? '#47b37c' : 'transparent',
                          color: page === 1 ? 'white' : '#133330'
                        }}
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                    
                    <PaginationItem>
                      <PaginationLink 
                        isActive={page === 2}
                        onClick={() => setPage(2)}
                        style={{ 
                          backgroundColor: page === 2 ? '#47b37c' : 'transparent',
                          color: page === 2 ? 'white' : '#133330'
                        }}
                      >
                        2
                      </PaginationLink>
                    </PaginationItem>
                    
                    <PaginationItem>
                      <PaginationLink 
                        onClick={page === data.items_stock_level.last_page ? undefined : () => setPage(Math.min(data.items_stock_level.last_page, page + 1))}
                        style={{ 
                          color: page === data.items_stock_level.last_page ? '#ccc' : '#47b37c',
                          pointerEvents: page === data.items_stock_level.last_page ? 'none' : 'auto',
                          opacity: page === data.items_stock_level.last_page ? 0.6 : 1
                        }}
                        aria-disabled={page === data.items_stock_level.last_page}
                      >
                        Next
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </PaginationLink>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Analytics Tab Content */}
          <TabsContent value="analytics" className="space-y-6 print:block">
            <h2 className="text-2xl font-bold" style={{ color: '#133330' }}>Inventory Analytics</h2>
            
            {/* Stock by Unit Type */}
            <Card style={{ backgroundColor: '#c8ffd9', border: 'none' }}>
              <CardHeader>
                <CardTitle style={{ color: '#133330' }}>Stock by Unit Type</CardTitle>
                <CardDescription>Breakdown of inventory by unit type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {data.summary.items_per_unit.map((unit: UnitType) => (
                    <div 
                      key={unit.unit_type_id} 
                      className="p-4 rounded-lg flex flex-col shadow-sm hover:shadow-md transition-shadow"
                      style={{ backgroundColor: 'white', border: '1px solid #47b37c' }}
                    >
                      <div className="text-sm text-muted-foreground">{unit.unit_type_name}</div>
                      <div className="mt-1 font-bold text-xl" style={{ color: '#133330' }}>{unit.total_stock_unit.toLocaleString()}</div>
                      <div className="text-sm" style={{ color: '#47b37c' }}>{formatCurrency(unit.total_stock_value)}</div>
                      <div className="mt-3 w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div className="h-full rounded-full" style={{ width: '80%', backgroundColor: '#47b37c' }}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            {/* Stock Level Analysis */}
            <Card>
              <CardHeader>
                <CardTitle style={{ color: '#133330' }}>Stock Level Analysis</CardTitle>
                <CardDescription>Breakdown of inventory by stock level categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="p-4 rounded-lg border border-red-200 bg-red-50">
                    <div className="text-sm text-red-800 font-medium">Low Stock Items</div>
                    <div className="mt-1 font-bold text-2xl text-red-800">
                      {data.items_stock_level.data.filter((item: StockItem) => item.total_stock <= 20).length}
                    </div>
                    <div className="text-sm text-red-600">
                      {((data.items_stock_level.data.filter((item: StockItem) => item.total_stock <= 20).length / data.summary.total_items) * 100).toFixed(1)}% of total inventory
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg border border-yellow-200 bg-yellow-50">
                    <div className="text-sm text-yellow-800 font-medium">Medium Stock Items</div>
                    <div className="mt-1 font-bold text-2xl text-yellow-800">
                      {data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 20 && item.total_stock <= 100).length}
                    </div>
                    <div className="text-sm text-yellow-600">
                      {((data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 20 && item.total_stock <= 100).length / data.summary.total_items) * 100).toFixed(1)}% of total inventory
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg border border-green-200 bg-green-50">
                    <div className="text-sm text-green-800 font-medium">Good Stock Items</div>
                    <div className="mt-1 font-bold text-2xl text-green-800">
                      {data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 100).length}
                    </div>
                    <div className="text-sm text-green-600">
                      {((data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 100).length / data.summary.total_items) * 100).toFixed(1)}% of total inventory
                    </div>
                  </div>
                </div>
                
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: 'Low Stock (≤20)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock <= 20).length, fill: '#f87171' },
                        { name: 'Medium Stock (21-100)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 20 && item.total_stock <= 100).length, fill: '#facc15' },
                        { name: 'Good Stock (>100)', value: data.items_stock_level.data.filter((item: StockItem) => item.total_stock > 100).length, fill: '#4ade80' }
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="value" name="Number of Items" fill="#47b37c" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            {/* Inventory Value Distribution */}
            <Card>
              <CardHeader>
                <CardTitle style={{ color: '#133330' }}>Financial Analysis</CardTitle>
                <CardDescription>Inventory value, expected revenue and profit metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2" style={{ color: '#133330' }}>Value Distribution</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1 text-sm">
                          <span className="text-muted-foreground">Stock Value</span>
                          <span className="font-medium" style={{ color: '#133330' }}>{formatCurrency(data.summary.total_stock_value)}</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div className="h-full" style={{ width: '40%', backgroundColor: '#47b37c' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1 text-sm">
                          <span className="text-muted-foreground">Expected Revenue</span>
                          <span className="font-medium" style={{ color: '#133330' }}>{formatCurrency(data.summary.expected_revenue)}</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div className="h-full" style={{ width: '72%', backgroundColor: '#3b82f6' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1 text-sm">
                          <span className="text-muted-foreground">Expected Profit</span>
                          <span className="font-medium" style={{ color: '#133330' }}>{formatCurrency(data.summary.expected_profit)}</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div className="h-full" style={{ width: '32%', backgroundColor: '#8b5cf6' }}></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: '#e6f7ef', border: '1px solid #47b37c' }}>
                      <h4 className="text-sm font-medium mb-2" style={{ color: '#133330' }}>Key Metrics</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-xs text-muted-foreground">Profit Margin</div>
                          <div className="text-lg font-bold" style={{ color: '#47b37c' }}>{profitMarginPercentage}%</div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Markup</div>
                          <div className="text-lg font-bold" style={{ color: '#47b37c' }}>
                            {((data.summary.expected_revenue / data.summary.total_stock_value - 1) * 100).toFixed(1)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Avg. Value/Item</div>
                          <div className="text-lg font-bold" style={{ color: '#47b37c' }}>
                            {formatCurrency(data.summary.total_stock_value / data.summary.total_items)}
                          </div>
                        </div>
                        <div>
                          <div className="text-xs text-muted-foreground">Avg. Profit/Item</div>
                          <div className="text-lg font-bold" style={{ color: '#47b37c' }}>
                            {formatCurrency(data.summary.expected_profit / data.summary.total_items)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium mb-4" style={{ color: '#133330' }}>Value vs. Profit Breakdown</h3>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Current', cost: data.summary.total_stock_value, revenue: data.summary.expected_revenue, profit: data.summary.expected_profit },
                          ]}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip formatter={(value) => [formatCurrency(Number(value)), '']} />
                          <Legend />
                          <Bar dataKey="cost" name="Stock Value" fill="#64748b" />
                          <Bar dataKey="revenue" name="Expected Revenue" fill="#3b82f6" />
                          <Bar dataKey="profit" name="Expected Profit" fill="#47b37c" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default InventoryStockReport;