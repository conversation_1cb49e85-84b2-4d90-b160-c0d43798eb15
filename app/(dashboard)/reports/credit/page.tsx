"use client";

import React from 'react';
import { CreditCard, Clock, Calendar, AlertCircle, CheckCircle, TrendingUp, RefreshCw } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useFetchCreditsReport } from '@/features/reports/api/use-fetch-credits-report';

const CreditReport = () => {
  
  const [organizationId, setOrganizationId] = React.useState(0);
  
  React.useEffect(() => {
    try {
      const organizationIdStr = localStorage.getItem("current_organization") || '';
      setOrganizationId(Number(organizationIdStr) || 0);
    } catch (error) {
      console.error("Error accessing localStorage:", error);
    }
  }, []);

  
  const { data: apiResponse, isLoading } = useFetchCreditsReport(
    organizationId ? { organization_id: organizationId } : undefined
  );

  console.log("Credit Report Data:", apiResponse);

  
  const defaultData = {
    total: 0,
    current: 0,
    "30_days": 0,
    "60_days": 0,
    "90_plus_days": 0
  };
  
  
  const creditData = apiResponse?.data || defaultData;

  
  if (isLoading || !organizationId) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="Credit Report"
          description="Monitor customer credit status and payment history"
          icon={CreditCard}
        />
         <div className="flex justify-center items-center h-64 border border-[#c8ffd9] rounded-lg bg-white mt-4">
              <div className="text-center">
                <RefreshCw className="h-10 w-10 animate-spin text-[#47b37c] mx-auto mb-4" />
                <p className="text-[#133330]">Loading credit report data...</p>
              </div>
            </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Credit Report"
           breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Credit Report" },
        ]}
        description="Monitor customer credit status and payment history"
        icon={CreditCard}
      />
      
      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Credit Card */}
        <Card className="border-[#133330] bg-[#133330] text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Credit</CardTitle>
            <TrendingUp className="h-4 w-4 text-[#47b37c]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">TSH {creditData.total.toLocaleString()}</div>
            <p className="text-xs text-[#c8ffd9]">Total outstanding credit</p>
          </CardContent>
        </Card>

        {/* Current Balance Card */}
        <Card className="border-[#47b37c]/20 bg-[#c8ffd9]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
            <CheckCircle className="h-4 w-4 text-[#47b37c]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#133330]">TSH {creditData.current.toLocaleString()}</div>
            <p className="text-xs text-[#133330]/80">Up to date payments</p>
          </CardContent>
        </Card>

        {/* 30 Days Overdue Card */}
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">30 Days Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">TSH {creditData["30_days"].toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Mild risk accounts</p>
          </CardContent>
        </Card>

        {/* 90+ Days Overdue Card */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">90+ Days Overdue</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">TSH {creditData["90_plus_days"].toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">High risk accounts</p>
          </CardContent>
        </Card>
      </div>

      {/* Credit Exposure Visualization */}
      <Card className="border-[#133330]/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#133330]">
            <Clock className="h-5 w-5" />
            <span>Credit Exposure Breakdown</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Current Balance */}
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-[#133330]">Current Balance</span>
                <span className="text-sm font-bold">TSH {creditData.current.toLocaleString()}</span>
              </div>
              <Progress 
                value={creditData.total > 0 ? (creditData.current / creditData.total) * 100 : 0} 
                className="h-3 bg-[#c8ffd9]" 
              />
            </div>
            
            {/* 90+ Days Overdue */}
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-[#133330]">90+ Days Overdue</span>
                <span className="text-sm font-bold">TSH {creditData["90_plus_days"].toLocaleString()}</span>
              </div>
              <Progress 
                value={creditData.total > 0 ? (creditData["90_plus_days"] / creditData.total) * 100 : 0} 
                className="h-3 bg-red-100" 
              />
              <p className="text-xs text-[#133330]/70 mt-1">
                This represents {creditData.total > 0 ? ((creditData["90_plus_days"] / creditData.total) * 100).toFixed(0) : 0}% of total outstanding credit
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Items */}
      <Card className="border-[#47b37c]/20">
        <CardHeader className="bg-[#133330] rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-white">
            <Calendar className="h-5 w-5 text-[#47b37c]" />
            <span>Recommended Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-4 bg-[#c8ffd9]/30 rounded-lg border border-[#47b37c]/20">
              <div className="flex-shrink-0 mt-1">
                <AlertCircle className="h-5 w-5 text-[#133330]" />
              </div>
              <div>
                <p className="font-medium text-[#133330]">Address Overdue Accounts</p>
                <p className="text-sm text-[#133330]/80">
                  TSH {creditData["90_plus_days"].toLocaleString()} is overdue by 90+ days. Implement a collection strategy:
                </p>
                <ul className="mt-2 space-y-1 text-sm text-[#133330]/80 pl-5 list-disc">
                  <li>Send reminder notices to debtors</li>
                  <li>Offer payment plans for large balances</li>
                  <li>Consider credit holds for repeat offenders</li>
                </ul>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-4 bg-[#c8ffd9]/10 rounded-lg">
              <div className="flex-shrink-0 mt-1">
                <CheckCircle className="h-5 w-5 text-[#47b37c]" />
              </div>
              <div>
                <p className="font-medium text-[#133330]">Maintain Current Processes</p>
                <p className="text-sm text-[#133330]/80">
                  Current accounts are in good standing. Continue with:
                </p>
                <ul className="mt-2 space-y-1 text-sm text-[#133330]/80 pl-5 list-disc">
                  <li>Regular payment reminders</li>
                  <li>Prompt invoicing</li>
                  <li>Clear credit terms communication</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default CreditReport;