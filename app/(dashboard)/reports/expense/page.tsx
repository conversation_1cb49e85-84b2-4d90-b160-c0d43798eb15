"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { 
  TrendingDown, DollarSign, <PERSON><PERSON><PERSON>, Calendar as CalendarIcon, 
  BarChart3, ArrowDown, ArrowUp, RefreshCw, Download, Plus 
} from "lucide-react";
import { <PERSON>Header } from "@/components/page-header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Area<PERSON>hart,
  Area,
  PieChart as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line
} from 'recharts';
import { format, subDays, parseISO } from 'date-fns';
import { FetchExpenseRequest } from '@/features/reports/types/report';
import { useFetchExpensesReport } from '@/features/reports/api/use-fetch-expenses';

interface ExpenseCategory {
  category_name: string;
  total_amount: number;
  trend: {
    period: string;
    number_of_expenses: number;
    total_amount: number;
  }[];
}



const COLORS = ['#47b37c', '#2a9d8f', '#0cc78b', '#83e377', '#a8e6cf', '#ffb347', '#ff7b54'];

const ExpenseReport = () => {
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 90),
    to: new Date()
  });

  const [isLoading, setIsLoading] = useState(false);
  const [view, setView] = useState<"summary" | "categories" | "trends">("summary");

  const [filters, setFilters] = useState<FetchExpenseRequest>(() => {
    // Safely get organization_id from localStorage
    const organizationId = typeof window !== 'undefined' ? localStorage.getItem("current_organization") || '' : '';
    return {
      organization_id: organizationId,
      start_date: subDays(new Date(), 90).toISOString(),
      end_date: new Date().toISOString(),
    };
  });

  const { data: expenseData, isLoading: apiLoading } = useFetchExpensesReport(filters);

  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      start_date: dateRange.from.toISOString(),
      end_date: dateRange.to.toISOString(),
    }));
  }, [dateRange]);

  useEffect(() => {
    setIsLoading(apiLoading);
  }, [apiLoading]);

  const calculateMetrics = useCallback(() => {
    if (!expenseData) return [];

    const totalExpenses = expenseData.total_expenses || 0;
    const totalCategories = expenseData.data?.length || 0;
    const totalTransactions = expenseData.data?.reduce(
      (sum: number, category: ExpenseCategory) => sum + (category.trend?.reduce(
        (sum: number, t) => sum + (t.number_of_expenses || 0), 0
      ) || 0), 0
    ) || 0;
    
    const previousPeriodExpenses = 125000; // This should come from your API
    const expenseChange = totalExpenses ? 
      ((totalExpenses - previousPeriodExpenses) / previousPeriodExpenses) * 100 : 0;
    
    return [
      {
        name: "Total Expenses",
        value: `TZS ${totalExpenses.toLocaleString()}`,
        change: `${Math.abs(expenseChange).toFixed(1)}%`,
        trend: expenseChange >= 0 ? "up" : "down",
        icon: DollarSign,
        color: expenseChange >= 0 ? "text-red-600" : "text-green-600"
      },
      {
        name: "Categories",
        value: totalCategories.toString(),
        change: "+1", // This should be dynamic
        trend: "up",
        icon: PieChart,
        color: "text-green-600"
      },
      {
        name: "Transactions",
        value: totalTransactions.toString(),
        change: "+3", // This should be dynamic
        trend: "up",
        icon: BarChart3,
        color: "text-green-600"
      }
    ];
  }, [expenseData]);

  const prepareCategoryData = useCallback(() => {
    if (!expenseData?.data) return [];
    
    return expenseData.data.map((category: ExpenseCategory) => ({
      name: category.category_name,
      value: category.total_amount,
      percentage: expenseData.total_expenses ? 
        Math.round((category.total_amount / expenseData.total_expenses) * 100) : 0
    }));
  }, [expenseData]);

  const prepareTrendData = useCallback(() => {
    if (!expenseData?.data) return [];

    const trendsByPeriod: Record<string, {
      date: string;
      formattedDate: string;
      transactions: number;
      amount: number;
    }> = {};
    
    expenseData.data.forEach((category: ExpenseCategory) => {
      category.trend?.forEach(trend => {
        if (!trend.period) return;
        
        if (!trendsByPeriod[trend.period]) {
          trendsByPeriod[trend.period] = {
            date: trend.period,
            formattedDate: format(parseISO(trend.period), "MMM d"),
            transactions: 0,
            amount: 0
          };
        }
        
        trendsByPeriod[trend.period].transactions += trend.number_of_expenses || 0;
        trendsByPeriod[trend.period].amount += trend.total_amount || 0;
      });
    });
    
    return Object.values(trendsByPeriod)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [expenseData]);

  // const getTopExpenseCategory = useCallback(() => {
  //   if (!expenseData?.data?.length) return null;
  //   return [...expenseData.data]
  //     .sort((a, b) => (b.total_amount || 0) - (a.total_amount || 0))[0];
  // }, [expenseData]);

  const handleDateRangeChange = useCallback((range: { from?: Date; to?: Date } | undefined) => {
    if (range && range.from && range.to) {
      setDateRange({ from: range.from, to: range.to });
    }
  }, []);

  const handleRefresh = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 800);
  }, []);

  // Derived data with null checks
  const metrics = calculateMetrics();
  const categoryData = prepareCategoryData();
  const trendData = prepareTrendData();


  if (!expenseData && !isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-red-500">Failed to load expense data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Expense Report"
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Expense Report" },
        ]}
        description={`Analysis for ${format(dateRange.from, "MMMM d, yyyy")} to ${format(dateRange.to, "MMMM d, yyyy")}`}
        icon={TrendingDown}
      />
      
      <div className="flex flex-col sm:flex-row justify-between gap-4 ">
        <Tabs defaultValue="summary" className="w-full " onValueChange={(value) => setView(value as "summary" | "categories" | "trends")}>
          <div className='flex items-center justify-between mb-4'>
          <TabsList className="grid w-full sm:w-[300px] grid-cols-3 ">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <div className="flex flex-col sm:flex-row gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant="outline"
                className="justify-start text-left font-normal border-[#c8ffd9] w-[250px]"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "MMM d, y")} -{" "}
                      {format(dateRange.to, "MMM d, y")}
                    </>
                  ) : (
                    format(dateRange.from, "MMM d, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
                required={false}
              />
            </PopoverContent>
          </Popover>
          
          <Button 
            onClick={handleRefresh}
            disabled={isLoading}
            className="bg-[#47b37c] hover:bg-[#3a9a6a]"
          >
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>

          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64 border border-[#c8ffd9] rounded-lg bg-white mt-4">
              <div className="text-center">
                <RefreshCw className="h-10 w-10 animate-spin text-[#47b37c] mx-auto mb-4" />
                <p className="text-[#133330]">Loading expense data...</p>
              </div>
            </div>
          ) : (
            <div className="">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {metrics.map((metric, index) => (
                <Card key={index} className="border border-[#c8ffd9] overflow-hidden relative">
                <div className="absolute top-0 right-0 h-16 w-16 bg-[#c8ffd9]/30 rounded-bl-full"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                  <div className="p-2 bg-[#c8ffd9] rounded-md">
                    <metric.icon className="h-5 w-5 text-[#133330]" />
                  </div>
                  <CardTitle className="text-sm font-medium text-[#133330]">
                    {metric.name}
                  </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-[#47b37c]">{metric.value}</div>
                  <div className={`flex items-center text-xs mt-1 ${metric.color}`}>
                  {metric.trend === "up" ? (
                    <ArrowUp className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 mr-1" />
                  )}
                  {metric.change} from last period
                  </div>
                </CardContent>
                </Card>
              ))}
              </div>

              <TabsContent value="summary" className="mt-0">
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mt-4">
                <Card className="lg:col-span-8 border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60">
                  <CardTitle className="flex items-center gap-2 text-[#133330]">
                  <TrendingDown className="h-5 w-5" />
                  Expense Trends
                  </CardTitle>
                  <CardDescription>Expense distribution over time</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <ResponsiveContainer width="100%" height={300}>
                  <AreaChart
                    data={trendData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <defs>
                    <linearGradient id="colorExpense" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#47b37c" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#47b37c" stopOpacity={0.1}/>
                    </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis dataKey="formattedDate" stroke="#666" />
                    <YAxis stroke="#666" />
                    <Tooltip
                    formatter={(value) => [`TZS ${Number(value).toLocaleString()}`, "Expenses"]}
                    contentStyle={{
                      backgroundColor: "white",
                      border: "1px solid #c8ffd9",
                      borderRadius: "4px"
                    }}
                    />
                    <Area
                    type="monotone"
                    dataKey="amount"
                    stroke="#47b37c"
                    fillOpacity={1}
                    fill="url(#colorExpense)"
                    name="Total Expenses"
                    />
                  </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
                </Card>

                <Card className="lg:col-span-4 border border-[#c8ffd9] flex flex-col justify-between">
                <CardHeader className="bg-[#c8ffd9]/60">
                  <CardTitle className="flex items-center gap-2 text-[#133330]">
                  <PieChart className="h-5 w-5" />
                  Expense Categories
                  </CardTitle>
                  <CardDescription>Distribution by category</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <ResponsiveContainer width="100%" height={200}>
                  <RechartsPieChart>
                    <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percentage }) => `${name}: ${percentage}%`}
                    >
                    {categoryData.map((entry: { name: string; value: number; percentage: number }, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`TZS ${Number(value).toLocaleString()}`, 'Amount']} />
                  </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
                </Card>
              </div>
              </TabsContent>

              <TabsContent value="categories" className="mt-0">
              <Card className="border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60 flex flex-row items-center justify-between px-6">
                <div>
                  <CardTitle className="flex items-center gap-2 text-[#133330]">
                  <PieChart className="h-5 w-5" />
                  Expense Categories
                  </CardTitle>
                  <CardDescription>
                  Detailed breakdown by expense category
                  </CardDescription>
                </div>
                
                </CardHeader>
                <CardContent className="p-0">
                <Table>
                  <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="w-[300px]">Category</TableHead>
                    <TableHead>Transactions</TableHead>
                    <TableHead>Latest Expense</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead className="text-right">% of Total</TableHead>
                  </TableRow>
                  </TableHeader>
                  <TableBody>
                  {expenseData?.data?.map((category: ExpenseCategory, index: number) => {
                    const totalTransactions = category.trend?.reduce(
                    (sum, t) => sum + (t.number_of_expenses || 0), 0
                    ) || 0;
                    const latestDate = category.trend?.length > 0 ? 
                    [...category.trend].sort(
                      (a, b) => new Date(b.period).getTime() - new Date(a.period).getTime()
                    )[0].period : null;
                    
                    const percentage = expenseData.total_expenses ? 
                    Math.round((category.total_amount / expenseData.total_expenses) * 100) : 0;
                    
                    return (
                    <TableRow key={index} className="hover:bg-[#c8ffd9]/20">
                      <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                        <span className="font-medium">{category.category_name}</span>
                      </div>
                      </TableCell>
                      <TableCell>{totalTransactions}</TableCell>
                      <TableCell>
                      {latestDate ? format(parseISO(latestDate), "MMM d, yyyy") : "N/A"}
                      </TableCell>
                      <TableCell className="font-medium">
                      TZS {category.total_amount.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <span>{percentage}%</span>
                      </div>
                      <Progress 
                        value={percentage} 
                        className="h-2 bg-gray-200 mt-1"
                      />
                      </TableCell>
                    </TableRow>
                    );
                  })}
                  </TableBody>
                </Table>
                </CardContent>
              </Card>
              </TabsContent>

              <TabsContent value="trends" className="mt-0">
              <Card className="border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60">
                <CardTitle className="flex items-center gap-2 text-[#133330]">
                  <TrendingDown className="h-5 w-5" />
                  Expense Trend Analysis
                </CardTitle>
                <CardDescription>Track expense patterns over time</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart
                  data={trendData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="formattedDate" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`TZS ${Number(value).toLocaleString()}`, "Amount"]}
                    contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #c8ffd9",
                    borderRadius: "4px"
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="amount"
                    stroke="#47b37c"
                    activeDot={{ r: 8 }}
                    name="Expense Amount"
                  />
                  </LineChart>
                </ResponsiveContainer>
                </CardContent>
              </Card>
              </TabsContent>
            </div>
          )}
        </Tabs>

        
      </div>
    </div>
  );
};

export default ExpenseReport;