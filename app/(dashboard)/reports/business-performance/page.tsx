
"use client";

import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, DollarSign, ShoppingCart, Box, Activity, 
  ArrowUp, ArrowDown, Calendar as CalendarIcon, RefreshCw,
  AlertCircle
} from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, 
  ResponsiveContainer, LineChart, Line, ComposedChart, Area,
  Legend
} from 'recharts';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format, subDays, parseISO, isWithinInterval } from 'date-fns';
import { Skeleton } from "@/components/ui/skeleton";
import { FetchBusinessPerformanceRequest, ReportData, TrendData } from '@/features/reports/types/report';
import { useFetchBusinessReport } from '@/features/reports/api/use-fetch-business';

// Format date to short format (MMM d)
const formatShortDate = (dateString: string) => {
  return format(parseISO(dateString), "MMM d");
};

const BusinessPerformanceReport = () => {
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date()
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);

  const [filters, setFilters] = useState<FetchBusinessPerformanceRequest>(() => {
    // Safely get organization_id from localStorage
    const organizationId = typeof window !== 'undefined' ? localStorage.getItem("current_organization") || '' : '';
    return {
      organization_id: organizationId,
      start_date: subDays(new Date(), 30).toISOString(),
      end_date: new Date().toISOString(),
    };
  });

  const { data: businesData, isLoading: apiLoading, error: apiError } = useFetchBusinessReport(filters);

  useEffect(() => {
    setFilters(prev => ({
      ...prev,
      start_date: dateRange.from.toISOString(),
      end_date: dateRange.to.toISOString(),
    }));
  }, [dateRange]);


  const formatCurrency = (value: number | string) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'TSH',
    }).format(num);
  };

  // Fetch data with error handling
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(apiLoading);
      setError(null);
      
      try {
        if (apiError) {
          throw apiError;
        }
        if (businesData) {
          setReportData(businesData);
        }
      } catch (err) {
        console.error("Failed to load report data:", err);
        setError("Failed to load report data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [businesData, apiLoading, apiError]);

  // Filter data by date range
  const filterData = (data: TrendData[] = []) => {
    return data.filter(item => {
      const itemDate = parseISO(item.transaction_date);
      return isWithinInterval(itemDate, { start: dateRange.from, end: dateRange.to });
    });
  };

  // Handle date range change
  const handleDateRangeChange = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      setDateRange({ from: range.from, to: range.to });
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setDateRange({
      from: subDays(new Date(), 30),
      to: new Date()
    });
  };

  // Prepare combined chart data
  const getCombinedChartData = () => {
    if (!reportData) return [];
    
    const salesData = reportData.sales_trends || [];
    const purchasesData = reportData.purchases_trends || [];
    const expensesData = reportData.expenses_trends || [];

    const allDates = new Set([
      ...salesData.map(item => item.transaction_date),
      ...purchasesData.map(item => item.transaction_date),
      ...expensesData.map(item => item.transaction_date)
    ]);

    return Array.from(allDates)
      .sort()
      .map(date => {
        const salesItem = salesData.find(item => item.transaction_date === date);
        const purchasesItem = purchasesData.find(item => item.transaction_date === date);
        const expensesItem = expensesData.find(item => item.transaction_date === date);

        return {
          date: formatShortDate(date),
          sales: salesItem ? parseFloat(salesItem.total_sales || "0") : 0,
          purchases: purchasesItem ? parseFloat(purchasesItem.total_purchases || "0") : 0,
          expenses: expensesItem ? parseFloat(expensesItem.total_expenses || "0") : 0
        };
      });
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 gap-4">
        <AlertCircle className="h-12 w-12 text-red-500" />
        <p className="text-red-500 text-lg">{error}</p>
        <Button 
          onClick={handleRefresh}
          className="bg-[#47b37c] hover:bg-[#3a9a6a]"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </div>
    );
  }

  if (isLoading || !reportData) {
    return (
      <div className="space-y-6">
        <PageHeader 
          title="Business Performance Report"
          breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Business Performance Report" },
        ]}
          description="Loading report data..."
          icon={TrendingUp}
        />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-4 w-3/4 mt-2" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-1/2" />
                <Skeleton className="h-4 w-1/3 mt-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader 
        title="Business Performance Report"
            breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Business Performance Report" },
        ]}
        description={`Report from ${format(dateRange.from, "MMM d, yyyy")} to ${format(dateRange.to, "MMM d, yyyy")}`}
        icon={TrendingUp}
      />
      
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          Last updated: {format(new Date(), "MMM d, yyyy 'at' h:mm a")}
        </div>
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="justify-start text-left font-normal border-[#47b37c] hover:bg-[#c8ffd9]/20 w-[250px]"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {format(dateRange.from, "MMM d, y")} - {format(dateRange.to, "MMM d, y")}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          <Button 
            onClick={handleRefresh}
            className="bg-[#47b37c] hover:bg-[#3a9a6a]"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Sales"
          value={reportData.total_sales || 0}
          change="+12.5%"
          icon={<DollarSign className="h-4 w-4 text-[#47b37c]" />}
          format={formatCurrency}
        />
        <MetricCard
           title="Cost of Goods Sold"
          value={reportData.cost_of_goods_sold || 0}
          change="+8.3%"
          icon={<ShoppingCart className="h-4 w-4 text-[#47b37c]" />}
          format={formatCurrency}
        />
        <MetricCard
          title="Net Profit"
          value={reportData.net_profit || 0}
          change="+15.2%"
          icon={<Activity className="h-4 w-4 text-[#47b37c]" />}
          format={formatCurrency}
        />
        <MetricCard
          title="Inventory Items"
          value={reportData.total_inventoryItems || 0}
          change="+2"
          icon={<Box className="h-4 w-4 text-[#47b37c]" />}
        />
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <TrendChart
          title="Sales Trends"
          description="Daily sales performance"
          data={filterData(reportData.sales_trends)}
          dataKey="total_sales"
          color="#47b37c"
          formatValue={formatCurrency}
        />
        <TrendChart
          title="Purchases Trends"
          description="Daily purchases activity"
          data={filterData(reportData.purchases_trends)}
          dataKey="total_purchases"
          color="#2a9d8f"
          formatValue={formatCurrency}
        />
        <TrendChart
          title="Expenses Trends"
          description="Daily expenses breakdown"
          data={filterData(reportData.expenses_trends)}
          dataKey="total_expenses"
          color="#ff7b54"
          formatValue={formatCurrency}
          chartType="line"
        />
        <CombinedTrendChart
          title="Performance Overview"
          description="Sales vs Purchases vs Expenses"
          data={getCombinedChartData()}
          formatValue={formatCurrency}
        />
      </div>

      {/* Financial Metrics */}
      <Card className="border-[#c8ffd9] bg-white shadow-sm">
        <CardHeader>
          <CardTitle className="text-[#133330]">Financial Metrics</CardTitle>
          <CardDescription>Key performance indicators</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <FinancialMetric
              title="Gross Profit Margin"
              value={(reportData.total_gross / parseFloat(reportData.total_sales?.toString() || "1")) * 100}
              unit="%"
              description="Higher is better"
            />
            <FinancialMetric
              title="Total Purchases"
              value={typeof reportData.total_purchases === "string" ? parseFloat(reportData.total_purchases) : (reportData.total_purchases || 0)}
              format={formatCurrency}
              description="Lower is better"
            />
            <FinancialMetric
              title="Token Consumption"
              value={reportData.token_consumption || 0}
              description="System usage"
            />
            <FinancialMetric
              title="Expenses Ratio"
              value={(parseFloat(reportData.total_expenses?.toString() || "0") / parseFloat(reportData.total_sales?.toString() || "1")) * 100}
              unit="%"
              description="Lower is better"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Reusable Metric Card Component
const MetricCard = ({
  title,
  value,
  change,
  icon,
  format = (val: string | number) => String(val),
}: {
  title: string;
  value: string | number;
  change: string;
  icon: React.ReactNode;
  format?: (val: string | number) => string;
}) => {
  const isPositive = change.startsWith('+');
  
  return (
    <Card className="border-[#c8ffd9] bg-white shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-[#133330]">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-[#47b37c]">
          {format(value)}
        </div>
        <p className={`text-xs mt-1 ${isPositive ? 'text-[#47b37c]' : 'text-[#ff7b54]'}`}>
          {isPositive ? (
            <ArrowUp className="inline h-3 w-3" />
          ) : (
            <ArrowDown className="inline h-3 w-3" />
          )}{' '}
          {change} from last period
        </p>
      </CardContent>
    </Card>
  );
};

// Reusable Trend Chart Component
const TrendChart = ({
  title,
  description,
  data = [],
  dataKey,
  color,
  formatValue,
  chartType = 'bar',
}: {
  title: string;
  description: string;
  data?: TrendData[];
  dataKey: string;
  color: string;
  formatValue: (val: number | string) => string;
  chartType?: 'bar' | 'line';
}) => {
  const chartData = data.map(item => ({
    date: formatShortDate(item.transaction_date),
    value: parseFloat((item[dataKey as keyof TrendData] as string) || "0"),
  })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return (
    <Card className="border-[#c8ffd9] bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="text-[#133330]">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[300px]">
          {chartData.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No data available for selected period</p>
            </div>
          ) : chartType === 'bar' ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="date" 
                  stroke="#133330"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  stroke="#133330"
                  tickFormatter={(value) => `$${(value / 1000)}k`}
                />
                <Tooltip 
                  formatter={(value) => [formatValue(value as number | string), title]}
                  contentStyle={{ 
                    backgroundColor: '#ffffff',
                    border: '1px solid #c8ffd9',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Bar 
                  dataKey="value" 
                  fill={color}
                  radius={[4, 4, 0, 0]}
                  name={title}
                />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="date" 
                  stroke="#133330"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  stroke="#133330"
                  tickFormatter={(value) => `$${(value / 1000)}k`}
                />
                <Tooltip 
                  formatter={(value) => [formatValue(value as number | string), title]}
                  contentStyle={{ 
                    backgroundColor: '#ffffff',
                    border: '1px solid #c8ffd9',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke={color}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                  name={title}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Combined Trend Chart Component
const CombinedTrendChart = ({
  title,
  description,
  data = [],
  formatValue,
}: {
  title: string;
  description: string;
  data?: { date: string; sales: number; purchases: number; expenses: number }[];
  formatValue: (val: number | string) => string;
}) => {
  return (
    <Card className="border-[#c8ffd9] bg-white shadow-sm">
      <CardHeader>
        <CardTitle className="text-[#133330]">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="h-[300px]">
          {data.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No data available for selected period</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={data}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                <XAxis 
                  dataKey="date" 
                  stroke="#133330"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  stroke="#133330"
                  tickFormatter={(value) => `$${(value / 1000)}k`}
                />
                <Tooltip 
                  formatter={(value: number, name: string) => [formatValue(value), name]}
                  contentStyle={{ 
                    backgroundColor: '#ffffff',
                    border: '1px solid #c8ffd9',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="sales" 
                  fill="#47b37c" 
                  fillOpacity={0.2}
                  stroke="#47b37c"
                  name="Sales"
                />
                <Area 
                  type="monotone" 
                  dataKey="purchases" 
                  fill="#2a9d8f" 
                  fillOpacity={0.2}
                  stroke="#2a9d8f"
                  name="Purchases"
                />
                <Area 
                  type="monotone" 
                  dataKey="expenses" 
                  fill="#ff7b54" 
                  fillOpacity={0.2}
                  stroke="#ff7b54"
                  name="Expenses"
                />
              </ComposedChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Financial Metric Component
const FinancialMetric = ({
  title,
  value,
  unit = '',
  format = (val: number) => val.toFixed(2),
  description,
}: {
  title: string;
  value: number;
  unit?: string;
  format?: (val: number) => string;
  description: string;
}) => {
  return (
    <div className="border border-[#c8ffd9] rounded-lg p-4">
      <div className="text-sm font-medium text-[#133330]">{title}</div>
      <div className="text-2xl font-bold text-[#47b37c]">
        {format(value)}{unit}
      </div>
      <div className="text-xs text-[#133330] mt-1">{description}</div>
    </div>
  );
};

export default BusinessPerformanceReport;