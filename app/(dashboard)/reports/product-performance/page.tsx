"use client";

import React, { useState, useEffect } from 'react';
import { 
  BarChart3, DollarSign, TrendingUp, Package, ArrowUp, ArrowDown, 
  AlertTriangle, PieChart, Star, RefreshCw, Calendar as CalendarIcon,
  Info, ShoppingCart, Search, ChevronLeft, ChevronRight
} from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from 'recharts';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, subDays } from 'date-fns';
import { FetchProductRequest, ProductData } from '@/features/reports/types/report';
import { useFetchProductReport } from '@/features/reports/api/use-fetch-product-perfomance';

// Interface for product data


// Interface for product data with status
interface ProductWithStatus extends ProductData {
  status: string;
}

const ProductPerformanceReport = () => {
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 7),
    to: new Date()
  });
    const getOrganizationId = () => {
    try {
      const organizationIdString = localStorage.getItem("current_organization");
      return organizationIdString ? Number(organizationIdString) : undefined;
    } catch (error) {
      console.error("Error accessing localStorage:", error);
      return undefined;
    }
  };

  const organizationId = getOrganizationId();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [stockThreshold, setStockThreshold] = useState(50); // Threshold for low stock alerts
  
  // Initialize filters separately from localStorage
  const [filters, setFilters] = useState<FetchProductRequest>({
    organization_id: organizationId,
    start_date: subDays(new Date(), 30).toISOString(),
    end_date: new Date().toISOString(),
  
  });

  // State to track if we've attempted to load data
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);

  // Safely get organization_id from localStorage after component mounts
 

  // Update filters when organization ID is loaded or other dependencies change
  useEffect(() => {
    if (!organizationId) return; // Only update filters if organizationId is present
    setFilters({
      organization_id: organizationId,
      start_date: dateRange.from.toISOString(),
      end_date: dateRange.to.toISOString()
    });
    setHasAttemptedLoad(true);
  }, [organizationId, dateRange, searchQuery, currentPage]);

  const { data: apiResponse, isLoading: apiLoading, isError, error, refetch } = useFetchProductReport(filters);
  
  // Extract the actual products data from the nested structure
  const productsData = apiResponse?.data;
  
  console.log("Products Data:===>", productsData);
  
  // Log debug info
  useEffect(() => {
    if (isError && error) {
      console.error("API Error:", error);
    }
    if (productsData) {
      console.log("Product data received:", productsData);
    }
  }, [isError, error, productsData]);

  // Handle date range change
  const handleDateRangeChange = (range: { from?: Date; to?: Date } | undefined) => {
    if (range?.from && range?.to) {
      setDateRange({ from: range.from, to: range.to });
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    if (productsData && page >= 1 && page <= productsData.last_page) {
      setCurrentPage(page);
    }
  };

  // Check if product data is valid and available
  const hasValidProductData = () => {
    return productsData && 
           productsData.data && 
           Array.isArray(productsData.data) && 
           productsData.data.length > 0;
  };

  // Calculate performance metrics based on product data
  const calculateMetrics = () => {
    if (!hasValidProductData()) return [];
    
    const productData = productsData.data;
    let totalRevenue = 0;
    let totalProfit = 0;
    let totalSold = 0;
    let lowStockCount = 0;
    
    // Use for loop instead of reduce to avoid potential issues
    for (let i = 0; i < productData.length; i++) {
      const product = productData[i];
      totalRevenue += parseFloat(product.revenue?.toString() || "0") || 0;
      totalProfit += parseFloat(product.profit?.toString() || "0") || 0;
      totalSold += product.no_sold || 0;
      if (product.in_stock < (product.reorder_level || stockThreshold)) {
        lowStockCount++;
      }
    }
    
    return [
      { 
        name: "Total Products", 
        value: productsData.total ? productsData.total.toString() : "0", 
        change: "+5%", 
        trend: "up", 
        icon: Package 
      },
      { 
        name: "Total Revenue", 
        value: `TZS ${totalRevenue.toLocaleString()}`, 
        change: "+12%", 
        trend: "up", 
        icon: DollarSign 
      },
      { 
        name: "Profit Margin", 
        value: totalRevenue > 0 ? `${Math.round((totalProfit / totalRevenue) * 100)}%` : "0%", 
        change: "+2%", 
        trend: "up", 
        icon: TrendingUp 
      },
      { 
        name: "Low Stock Items", 
        value: lowStockCount.toString(), 
        change: lowStockCount > 5 ? "+2" : "-1", 
        trend: lowStockCount > 5 ? "up" : "down", 
        icon: AlertTriangle 
      },
    ];
  };

  // Calculate category distribution
  const calculateCategoryDistribution = () => {
    if (!hasValidProductData()) return [];
    
    const productData = productsData.data;
    const categories: {
      [key: string]: {
        name: string;
        value: number;
        revenue: number;
        profit: number;
      };
    } = {};
    
    // Use for loop to avoid array method issues
    for (let i = 0; i < productData.length; i++) {
      const product = productData[i];
      const category = product.inventory_category_id ? 
        `Category ${product.inventory_category_id}` : "Uncategorized";
      
      if (!categories[category]) {
        categories[category] = {
          name: category,
          value: 0,
          revenue: 0,
          profit: 0
        };
      }
      
      categories[category].value += (product.no_sold || 0);
      categories[category].revenue += parseFloat(product.revenue?.toString() || "0") || 0;
      categories[category].profit += parseFloat(product.profit?.toString() || "0") || 0;
    }
    
    // Calculate total items sold for percentage
    let totalSold = 0;
    for (let i = 0; i < productData.length; i++) {
      totalSold += (productData[i].no_sold || 0);
    }
    
    // Convert to array and calculate percentages
    return Object.values(categories).map(cat => ({
      ...cat,
      percentage: totalSold > 0 ? Math.round((cat.value / totalSold) * 100) || 0 : 0
    }));
  };

  // Get low stock items
  const getLowStockItems = () => {
    if (!hasValidProductData()) return [];
    
    const productData = productsData.data;
    const lowStockItems: ProductWithStatus[] = [];
    
    // Use for loop to gather low stock items
    for (let i = 0; i < productData.length; i++) {
      const product = productData[i];
      if (product.in_stock < (product.reorder_level || stockThreshold)) {
        lowStockItems.push({
          ...product,
          status: product.in_stock < (product.reorder_level || stockThreshold) * 0.5 ? "Critical" : "Low Stock"
        });
      }
    }
    
    // Sort by in_stock level (ascending)
    return lowStockItems.sort((a, b) => a.in_stock - b.in_stock);
  };

  // Get top performing product
  const getTopPerformingProduct = () => {
    if (!hasValidProductData()) return null;
    
    const productData = productsData.data;
    
    // Find product with highest revenue
    let topProduct = productData[0];
    for (let i = 1; i < productData.length; i++) {
      const currentRevenue = parseFloat(productData[i].revenue?.toString() || "0") || 0;
      const topRevenue = parseFloat(topProduct.revenue?.toString() || "0") || 0;
      if (currentRevenue > topRevenue) {
        topProduct = productData[i];
      }
    }
    
    return topProduct;
  };

  // Derived state
  const metrics = calculateMetrics();
  const categoryDistribution = calculateCategoryDistribution();
  const lowStockItems = getLowStockItems();
  const topProduct = getTopPerformingProduct();
  
  // Color constants
  const COLORS = ['#47b37c', '#2a9d8f', '#0cc78b', '#83e377', '#a8e6cf', '#ffb347', '#ff7b54'];



  return (
    <div className="space-y-6">
      <PageHeader 
        title="Product Performance Report"
           breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Reports" },
          { name: "Product Performance Report" },
        ]}
        description={`Analysis for ${format(dateRange.from, "MMMM d, yyyy")} to ${format(dateRange.to, "MMMM d, yyyy")}`}
        icon={BarChart3}
      />
      
      {/* Report Controls with Date Range Picker */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant="outline"
                className="justify-start text-left font-normal border-[#c8ffd9] w-[250px]"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "MMM d, y")} -{" "}
                      {format(dateRange.to, "MMM d, y")}
                    </>
                  ) : (
                    format(dateRange.from, "MMM d, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          <form onSubmit={handleSearch} className="flex gap-2">
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-[200px] border-[#c8ffd9]"
            />
            <Button type="submit" variant="outline" className="border-[#c8ffd9]">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </div>
        
        <Button 
          onClick={() => refetch()}
          disabled={apiLoading}
          className="bg-[#47b37c] hover:bg-[#3a9a6a]"
        >
          {apiLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {/* Loading state */}
      {apiLoading && (
        <div className="flex justify-center items-center h-64 border border-[#c8ffd9] rounded-lg bg-white">
          <div className="text-center">
            <RefreshCw className="h-10 w-10 animate-spin text-[#47b37c] mx-auto mb-4" />
            <p className="text-[#133330]">Loading product data...</p>
          </div>
        </div>
      )}

      {/* No data state */}
      {!apiLoading && !hasValidProductData() && (
        <div className="flex justify-center items-center h-64 border border-[#c8ffd9] rounded-lg bg-white">
          <div className="text-center">
            <Info className="h-10 w-10 text-[#47b37c] mx-auto mb-4" />
            <p className="text-[#133330] font-medium">No product data available</p>
            <p className="text-gray-600 mt-2">Try adjusting your search criteria or date range.</p>
            {isError && (
              <div className="mt-4">
                <p className="text-red-500">There was an error loading the data.</p>
                <Button 
                  onClick={() => refetch()}
                  className="mt-2 bg-red-600 hover:bg-red-700"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content when data is available */}
      {!apiLoading && hasValidProductData() && (
        <>
          {/* Warning banner for error but with data */}
          {isError && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    There was an issue with the data request, but some data was loaded. 
                    Results may be incomplete or inaccurate.
                  </p>
                </div>
              </div>
            </div>
          )}
        
          {/* Performance Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {metrics.map((metric, index) => (
              <Card key={index} className="border border-[#c8ffd9] overflow-hidden">
                <div className="absolute top-0 right-0 h-16 w-16 bg-[#c8ffd9]/30 rounded-bl-full"></div>
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-[#c8ffd9] rounded-md">
                      <metric.icon className="h-5 w-5 text-[#133330]" />
                    </div>
                    <CardTitle className="text-sm font-medium text-[#133330]">
                      {metric.name}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-[#47b37c]">{metric.value}</div>
                  <div className={`flex items-center text-xs mt-1 ${
                    metric.trend === "up" ? "text-green-600" : "text-red-600"
                  }`}>
                    {metric.trend === "up" ? (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {metric.change} from last period
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column */}
            <div className="lg:col-span-2 space-y-6">
              {/* Product Performance Table */}
              <Card className="border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60 flex flex-row items-center justify-between px-6">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-[#133330]">
                      <ShoppingCart className="h-5 w-5" />
                      Product Performance
                    </CardTitle>
                    <CardDescription>
                      Showing {productsData.data.length} of {productsData.total || 0} products 
                      (Page {productsData.current_page || 1} of {productsData.last_page || 1})
                    </CardDescription>
                  </div>
                  <Select defaultValue="revenue">
                    <SelectTrigger className="w-[150px] bg-white border-[#47b37c]">
                      <SelectValue placeholder="Sort By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="revenue">Revenue (High-Low)</SelectItem>
                      <SelectItem value="sales">Sales Volume</SelectItem>
                      <SelectItem value="stock">Stock Level</SelectItem>
                    </SelectContent>
                  </Select>
                </CardHeader>
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow className="hover:bg-transparent">
                        <TableHead className="w-[300px]">Product</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Sales</TableHead>
                        <TableHead>Revenue</TableHead>
                        <TableHead>Profit</TableHead>
                        <TableHead>Stock</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {productsData.data.map((product: ProductData) => (
                        <TableRow key={product.id} className="hover:bg-[#c8ffd9]/20">
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100">
                              
                                  <Package className="h-6 w-6 m-2 text-gray-400" />
                                
                              </div>
                              <div>
                                <div className="font-medium">{product.name}</div>
                                <div className="text-xs text-gray-500">{product.inventory_item_sku}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>TZS {parseFloat(product.sale_price || "0").toLocaleString()}</TableCell>
                          <TableCell>{product.no_sold || 0}</TableCell>
                          <TableCell>TZS {(parseFloat(product.revenue?.toString() || "0") || 0).toLocaleString()}</TableCell>
                          <TableCell>TZS {(parseFloat(product.profit?.toString() || "0") || 0).toLocaleString()}</TableCell>
                          <TableCell>
                            <Badge 
                              variant={product.in_stock < (product.reorder_level || stockThreshold) ? "destructive" : "outline"} 
                              className={product.in_stock < (product.reorder_level || stockThreshold) ? "" : "bg-[#c8ffd9] text-[#133330]"}
                            >
                              {product.in_stock}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
                <CardFooter className="flex justify-between py-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="border-[#c8ffd9]"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                  </Button>
                  <div className="text-sm text-muted-foreground">
                    Page {productsData.current_page || 1} of {productsData.last_page || 1}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!productsData.last_page || currentPage === productsData.last_page}
                    className="border-[#c8ffd9]"
                  >
                    Next <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Product Spotlight */}
              {topProduct && (
                <Card className="border border-[#c8ffd9]">
                  <CardHeader className="bg-[#c8ffd9]/60">
                    <CardTitle className="flex items-center gap-2 text-[#133330]">
                      <Star className="h-5 w-5" />
                      Product Spotlight
                    </CardTitle>
                    <CardDescription>Best performing product this period</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center">
                      <div className="w-20 h-20 rounded-full overflow-hidden bg-[#c8ffd9]/30 mb-4 flex items-center justify-center">
                     
                          <Package className="h-10 w-10 text-[#47b37c]" />
                        
                      </div>
                      <h3 className="text-xl font-bold text-[#133330]">{topProduct.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {topProduct.inventory_category_id ? `Category ${topProduct.inventory_category_id}` : "Uncategorized"}
                      </p>
                      
                      <div className="grid grid-cols-2 gap-6 w-full mt-6">
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-gray-500">Price</span>
                          <span className="text-lg font-bold text-[#47b37c]">
                            TZS {parseFloat(topProduct.sale_price || "0").toLocaleString()}
                          </span>
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-gray-500">Sales</span>
                          <span className="text-lg font-bold text-[#47b37c]">
                            {topProduct.no_sold || 0} units
                          </span>
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-gray-500">Revenue</span>
                          <span className="text-lg font-bold text-[#47b37c]">
                            TZS {(parseFloat(topProduct.revenue?.toString() || "0") || 0).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-gray-500">Stock</span>
                          <span className={`text-lg font-bold ${
                            topProduct.in_stock < (topProduct.reorder_level || stockThreshold) ? "text-red-500" : "text-[#47b37c]"
                          }`}>
                            {topProduct.in_stock} units
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-center pb-6">
                    <Button className="bg-[#47b37c] hover:bg-[#3a9a6a]">View Details</Button>
                  </CardFooter>
                </Card>
              )}

              {/* Category Breakdown */}
              <Card className="border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60">
                  <CardTitle className="flex items-center gap-2 text-[#133330]">
                    <PieChart className="h-5 w-5" />
                    Category Distribution
                  </CardTitle>
                  <CardDescription>Sales distribution by category</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  {categoryDistribution.length > 0 ? (
                    <>
                      <ResponsiveContainer width="100%" height={200}>
                        <RechartsPieChart>
                          <Pie
                            data={categoryDistribution}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {categoryDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value, name) => [`${value} units`, name]} />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                      
                      <div className="mt-4 space-y-3">
                        {categoryDistribution.map((category, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                              <span className="text-sm">{category.name}</span>
                            </div>
                            <span className="text-sm font-medium">TZS {category.revenue.toLocaleString()}</span>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-center">
                      <Info className="h-10 w-10 text-[#47b37c] mb-2" />
                      <p className="text-[#133330]">No category data available</p>
                      <p className="text-sm text-gray-500">Try adjusting your date range</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Inventory Alerts */}
              <Card className="border border-[#c8ffd9]">
                <CardHeader className="bg-[#c8ffd9]/60">
                  <CardTitle className="flex items-center gap-2 text-[#133330]">
                    <AlertTriangle className="h-5 w-5" />
                    Inventory Alerts
                  </CardTitle>
                  <CardDescription>Products requiring attention</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  {lowStockItems.length > 0 ? (
                    <div className="space-y-4">
                      {lowStockItems.map((item) => (
                        <div key={item.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-200">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-full ${
                              item.status === "Critical" ? "bg-red-100" : "bg-yellow-100"
                            }`}>
                              <AlertTriangle className={`h-4 w-4 ${
                                item.status === "Critical" ? "text-red-500" : "text-yellow-500"
                              }`} />
                            </div>
                            <div>
                              <h4 className="font-medium text-sm">{item.name}</h4>
                              <p className={`text-xs ${
                                item.status === "Critical" ? "text-red-500" : "text-yellow-500"
                              }`}>
                                {item.status} - {item.in_stock} units left
                              </p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" className="h-8 px-2">
                            <Package className="h-4 w-4 mr-1" />
                            Reorder
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-center">
                      <Info className="h-10 w-10 text-[#47b37c] mb-2" />
                      <p className="text-[#133330]">No low stock items found</p>
                      <p className="text-sm text-gray-500">All products are above reorder levels</p>
                    </div>
                  )}
                </CardContent>
                {lowStockItems.length > 0 && (
                  <CardFooter className="flex justify-center pb-6">
                    <Button variant="outline" className="border-[#47b37c] text-[#133330] hover:bg-[#c8ffd9]">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      View All Alerts
                    </Button>
                  </CardFooter>
                )}
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProductPerformanceReport;