"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Row } from "@tanstack/react-table";

import { Download, Filter, Plus, Trash, Receipt, FolderOpen, TrendingDown } from "lucide-react";
import { useEffect, useState } from "react";
import { FaFileExcel } from "react-icons/fa";
import { useRouter, useSearchParams } from "next/navigation";
import { PageHeader } from "@/components/page-header";
import { ExpenseCategoryTable } from "@/features/expense_category/components/expense-category-table";
import { ExpenseTable } from "@/features/expenses/components/expense-table";
import { Expense, FetchExpensesRequest, FetchExpensesResponse } from "@/features/expenses/types/expenses";
import { ExpenseCategory, FetchExpenseCategoryRequest, FetchExpenseCategoryResponse } from "@/features/expense_category/types/inventoryCategory";
import { useExpenseCategoryFormModal } from "@/features/expense_category/hooks/use-expense-category-form-modal";
import { useExpenseFormModal } from "@/features/expenses/hooks/use-expense-modal-form";

export default function ExpensesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get tab from URL or default to "expenses"
  const currentTab = searchParams.get("tab") || "expenses";

  // Record Expense state
  const [selectedExpenseRows, setSelectedExpenseRows] = useState<Row<Expense>[]>([]);
  const [expenseResponse, setExpenseResponse] = useState<FetchExpensesResponse | null>(null);
  const [expenseSearch, setExpenseSearch] = useState<string>("");
  const [expenseFilters, setExpenseFilters] = useState<FetchExpensesRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });
  const expense = useExpenseFormModal();

  // Expense Categories state
  const [selectedCategoryRows, setSelectedCategoryRows] = useState<Row<ExpenseCategory>[]>([]);
  const [categoryResponse, setCategoryResponse] = useState<FetchExpenseCategoryResponse | null>(null);
  const [categorySearch, setCategorySearch] = useState<string>("");
  const [categoryFilters, setCategoryFilters] = useState<FetchExpenseCategoryRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });
  const category = useExpenseCategoryFormModal();

  // Handle tab change
  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", value);
    router.push(`/expenses?${params.toString()}`);
  };

  useEffect(() => {
    setExpenseFilters((prev: FetchExpensesRequest) => ({
      ...prev,
      search: expenseSearch,
    }));
  }, [expenseSearch]);

  useEffect(() => {
    setCategoryFilters((prev: FetchExpenseCategoryRequest) => ({
      ...prev,
      search: categorySearch,
    }));
  }, [categorySearch]);

  return (
    <div className="space-y-6">
      <PageHeader 
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Expense Management" },
        ]}
        title="Expense Management"
        description="Track business expenses and organize expense categories"
        icon={TrendingDown}
      />

      <Tabs value={currentTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Record Expense
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Expense Categories
          </TabsTrigger>
        </TabsList>

        {/* Record Expense Tab */}
        <TabsContent value="expenses" className="space-y-4">
          <Card>
            <CardHeader className="gap-y-2 flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  Expense Records
                </CardTitle>
                <CardDescription className="text-sm">
                  {expenseResponse?.total ?? 0} expenses recorded
                </CardDescription>
              </div>
              <Button
                onClick={() => {
                  expense.open()
                }}
                size="sm"
                className="mt-4"
              >
                <Plus className="size-4 mr-2" />
                Record Expense
              </Button>
            </CardHeader>

            <CardContent>
              <div className="mb-2 flex justify-start items-stretch gap-x-2">
                <input
                  className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
                  placeholder="Search expenses..."
                  value={expenseSearch}
                  onChange={(e) => setExpenseSearch(e.target.value)}
                />
                <div className="flex-1"></div>

                <Button
                  size="sm"
                  variant="destructive"
                  disabled={selectedExpenseRows.length == 0}
                >
                  <Trash /> Bulk Delete{" "}
                  {selectedExpenseRows.length > 0 && (
                    <Badge variant="secondary">{selectedExpenseRows.length}</Badge>
                  )}
                </Button>
                <Button size="sm" variant="secondary">
                  <FaFileExcel />
                  <Download />
                </Button>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button size="sm" variant="secondary">
                      <Filter />
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent side="left" className="w-80">
                    Filters
                  </PopoverContent>
                </Popover>
              </div>
              
              <ExpenseTable
                onRowSelectedChanged={setSelectedExpenseRows}
                onDataChange={(res) => res && setExpenseResponse(res)}
                filters={expenseFilters}
                organizationId={localStorage.getItem("current_organization")!}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Expense Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader className="gap-y-2 flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  Expense Categories
                </CardTitle>
                <CardDescription className="text-sm">
                  {categoryResponse?.total ?? 0} categories available
                </CardDescription>
              </div>
              <Button
                onClick={() => {
                  category.open()
                }}
                size="sm"
                className="mt-4"
              >
                <Plus className="size-4 mr-2" />
                Add Category
              </Button>
            </CardHeader>

            <CardContent>
              <div className="mb-2 flex justify-start items-stretch gap-x-2">
                <input
                  className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
                  placeholder="Search categories..."
                  value={categorySearch}
                  onChange={(e) => setCategorySearch(e.target.value)}
                />
                <div className="flex-1"></div>

                <Button
                  size="sm"
                  variant="destructive"
                  disabled={selectedCategoryRows.length == 0}
                >
                  <Trash /> Bulk Delete{" "}
                  {selectedCategoryRows.length > 0 && (
                    <Badge variant="secondary">{selectedCategoryRows.length}</Badge>
                  )}
                </Button>
                <Button size="sm" variant="secondary">
                  <FaFileExcel />
                  <Download />
                </Button>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button size="sm" variant="secondary">
                      <Filter />
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent side="left" className="w-80">
                    Filters
                  </PopoverContent>
                </Popover>
              </div>
              
              <ExpenseCategoryTable
                onRowSelectedChanged={setSelectedCategoryRows}
                onDataChange={(res) => res && setCategoryResponse(res)}
                filters={categoryFilters}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
