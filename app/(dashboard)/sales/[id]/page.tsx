"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import React, { useMemo } from "react";
import { useRetrieveSale } from "@/features/sales/api/use-retrieve-sale";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft, DollarSign, Package, User, ShoppingCart } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { formatCurrency, decryptId, encryptId } from "@/lib/utils";

export default function SaleDetailPage() {
  const params = useParams();
  const router = useRouter();

  // Safely extract and convert the ID parameter using useMemo to prevent re-computation
  const saleId = useMemo(() => {
    const rawId = params?.id;
    const encryptedId = typeof rawId === 'string' ? rawId : String(rawId || '');
    return decryptId(encryptedId);
  }, [params?.id]);

  const { data: response, isLoading, error } = useRetrieveSale(saleId);
  const sale: any = response?.data;

  const getStatusColor = (status?: string) => {
    switch ((status ?? '').toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "submited":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "draft":
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <div>Loading sale details...</div>
      </div>
    );
  }

  if (error || !sale) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <div>
          <p>Sale not found</p>
          <p>Error: {error ? JSON.stringify(error) : 'No sale data'}</p>
          <p>Sale ID: {saleId}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Sale Details</h1>
            <p className="text-muted-foreground text-sm sm:text-base">{sale.code}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="xl:col-span-2 space-y-6">
          {/* Sale Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" /> Sale Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <Badge className={getStatusColor(sale.status)}>
                    {String(sale.status).toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Date</p>
                  <p className="font-medium">{formatDate(sale.date)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                  <p className="text-lg font-bold">{formatCurrency(Number(sale.total_amount))}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Received Amount</p>
                  <p className="text-lg font-bold text-green-600">{formatCurrency(Number(sale.received_amount ?? 0))}</p>
                </div>
                {sale.is_credit_sale && (
                  <>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Credit Amount</p>
                      <p className="text-lg font-bold text-orange-600">{formatCurrency(Number(sale.amount_credit ?? 0))}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Sale Type</p>
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                        {sale.sale_type?.toUpperCase() || 'CREDIT'}
                      </Badge>
                    </div>
                  </>
                )}
                {sale.return_amount && Number(sale.return_amount) > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Return Amount</p>
                    <p className="text-lg font-bold text-red-600">{formatCurrency(Number(sale.return_amount))}</p>
                  </div>
                )}
              </div>
              {sale.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Description</p>
                  <p className="text-sm">{sale.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" /> Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              {sale.sale_items && sale.sale_items.length > 0 ? (
                <div className="space-y-4">
                  {sale.sale_items.map((item: any) => (
                    <div
                      key={item.id}
                      className="border rounded-lg p-3 sm:p-4 cursor-pointer hover:bg-gray-50 transition"
                      onClick={() => {
                        if (item.item?.id) {
                            window.open(`/inventories/${encryptId(item.item.id)}`, '_blank');
                        }
                      }}
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div>
                          <div className="font-medium">{item.item?.name ?? 'Item'}</div>
                          <div className="text-xs text-muted-foreground">
                            SKU: {item.item?.inventory_item_sku ?? '-'}
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="font-semibold">{formatCurrency(Number(item.unit_price))}</p>
                          <p className="text-xs text-muted-foreground">
                            Qty: {item.quantity} · Total: {formatCurrency(Number(item.quantity) * Number(item.unit_price))}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No items recorded</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6 xl:space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" /> Customer
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Name</p>
                <p className="font-medium">{sale.customer?.name || "N/A"}</p>
              </div>
              {sale.customer?.phone && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone</p>
                  <p className="font-medium">{sale.customer.phone}</p>
                </div>
              )}
              {sale.customer?.email && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <p className="font-medium">{sale.customer.email}</p>
                </div>
              )}
              {sale.customer?.address && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Address</p>
                  <p className="font-medium">{sale.customer.address}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Created By Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" /> Created By
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Name</p>
                <p className="font-medium">{sale.users?.name || "N/A"}</p>
              </div>
              {sale.users?.phone && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone</p>
                  <p className="font-medium">{sale.users.phone}</p>
                </div>
              )}
              {sale.users?.email && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <p className="font-medium">{sale.users.email}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" /> Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                <p className="font-medium">{sale.sale_items?.length || 0}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Quantity</p>
                <p className="font-medium text-blue-600">
                  {sale.sale_items?.reduce((sum: number, i: any) => sum + Number(i.quantity || 0), 0)}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                <p className="font-medium text-green-600">{formatCurrency(Number(sale.total_amount || 0))}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p className="font-medium">
                  {sale.updated_at ? formatDistanceToNow(new Date(sale.updated_at), { addSuffix: true }) : '-'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}