"use client";
import { 
  LayoutDashboard, 
  Users, 
  ShoppingCart, 
  Package, 
  DollarSign, 
  TrendingUp, 
  User<PERSON>heck, 
  Star,
  Award,
  BarChart2,
  ShoppingBag,
  Activity,
  ArrowUpRight,
  PlusCircle,
  RefreshCw
} from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useStateManager } from "@/hooks/use-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { QuickActions } from "@/features/dashboard/components/quick-actions";
import { Button } from "@/components/ui/button";
import { useTokenPurchaseModal } from "@/features/tokens/hooks/use-token-purchase-modal";
import { useDashboardStatic } from "@/features/reports/api/use-fetch-dashboard";

const OrganizationDashboard = () => {
  // Safely get organization ID from localStorage
  const getOrganizationId = () => {
    try {
      const organizationIdString = localStorage.getItem("current_organization");
      return organizationIdString ? Number(organizationIdString) : undefined;
    } catch (error) {
      console.error("Error accessing localStorage:", error);
      return undefined;
    }
  };

  const organizationId = getOrganizationId();
  const { open } = useTokenPurchaseModal();
  const { currentOrganization } = useStateManager();
  
  // Only fetch data if organizationId exists
  const { data, isLoading } = useDashboardStatic(organizationId || 0);

  console.log('data=====>dashboard--->', data);

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'TSH',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  };

  const handleAddTokens = () => {
    open();
  };

  

  // Show loading state if data is being fetched
  if (isLoading || !data) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
        <div className="flex flex-col items-center gap-4">
          <RefreshCw className="h-10 w-10 animate-spin text-[#47b37c]" />
          <div>Loading dashboard data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description={`Welcome back to ${currentOrganization?.name || "Mosha Shop"}! Here's your business overview.`}
        icon={LayoutDashboard}
        gradientText
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" }
        ]}
      />

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Customers Card */}
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#c8ffd9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Customers
            </CardTitle>
            <Users className="h-5 w-5" style={{ color: '#133330' }} />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold" style={{ color: '#133330' }}>{data?.customer_stats?.total_customers || 0}</div>
            <div className="flex items-center pt-2">
              <TrendingUp className="h-4 w-4 mr-1" style={{ color: '#47b37c' }} />
              <span className="text-xs" style={{ color: '#47b37c' }}>
                {data?.customer_stats?.customer_growth_rate || 0}% growth
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Products Card */}
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#c8ffd9' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Products
            </CardTitle>
            <Package className="h-5 w-5" style={{ color: '#133330' }} />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold" style={{ color: '#133330' }}>{data?.product_stats?.total_products || 0}</div>
            <div className="flex items-center pt-2">
              <Badge variant="secondary" className="text-xs" style={{ backgroundColor: (data?.product_stats?.low_stock_products || 0) > 0 ? '#ffeb3b' : '#c8ffd9' }}>
                {data?.product_stats?.low_stock_products || 0} low in stock
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Sales Card */}
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#47b37c', color: 'white' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              30-Day Sales
            </CardTitle>
            <DollarSign className="h-5 w-5" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{formatCurrency(data?.financial_overview?.total_sales_last_30_days || 0)}</div>
            <div className="flex items-center pt-2">
              <Activity className="h-4 w-4 mr-1" />
              <span className="text-xs">
                {data?.financial_overview?.net_profit_margin || 0}% profit margin
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Tokens Card */}
        <Card className="border-0 shadow-sm" style={{ backgroundColor: '#133330', color: 'white' }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Token Balance
            </CardTitle>
            <Button
              size="sm"
              variant="outline"
              onClick={handleAddTokens}
              className="border-amber-300 text-amber-700 hover:bg-amber-100"
            >
              <PlusCircle className="h-4 w-4 mr-1" />
              Buy
            </Button>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{Number(data?.token_stats?.current_token_balance || 0).toFixed(2)}</div>
            <div className="flex items-center pt-2">
              <ShoppingCart className="h-4 w-4 mr-1" />
              <span className="text-xs">
                {data?.token_stats?.tokens_consumed_last_30_days || 0} used last month
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Area */}
      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
        {/* Top Products Section */}
        <div className="lg:col-span-2 space-y-6">
          {/* Top Products by Revenue */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="border-b">
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" style={{ color: '#47b37c' }} />
                <span>Top Products by Revenue</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="w-[50px]">Rank</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Revenue</TableHead>
                    <TableHead className="text-right">Sales</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.product_stats?.top_products_by_revenue?.map(
                    (
                      product: { id: number | string; name: string; total_revenue: number },
                      index: number
                    ) => (
                    <TableRow key={product.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <TableCell>
                        <Badge variant="outline" className="px-2 py-1" style={{ 
                          backgroundColor: index < 3 ? '#47b37c' : '#c8ffd9',
                          color: index < 3 ? 'white' : '#133330'
                        }}>
                          #{index + 1}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell className="text-right">{formatCurrency(product.total_revenue)}</TableCell>
                      <TableCell className="text-right">
                        {data?.product_stats?.top_products_by_quantity?.find((p: { id: number | string; total_quantity_sold: number; name: string }) => p.id === product.id)?.total_quantity_sold || 'N/A'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Top Products by Quantity */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="border-b">
              <CardTitle className="flex items-center gap-2">
                <BarChart2 className="h-5 w-5" style={{ color: '#47b37c' }} />
                <span>Top Products by Quantity Sold</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader className="bg-gray-50">
                  <TableRow>
                    <TableHead className="w-[50px]">Rank</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Quantity Sold</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.product_stats?.top_products_by_quantity?.map(
                    (
                      product: { id: number | string; name: string; total_quantity_sold: number },
                      index: number
                    ) => (
                    <TableRow key={product.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <TableCell>
                        <Badge variant="outline" className="px-2 py-1" style={{ 
                          backgroundColor: index < 3 ? '#47b37c' : '#c8ffd9',
                          color: index < 3 ? 'white' : '#133330'
                        }}>
                          #{index + 1}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell className="text-right">{product.total_quantity_sold}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Customer Insights Section */}
        <div className="space-y-6">
          {/* Top Customers by Spending */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="border-b">
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" style={{ color: '#47b37c' }} />
                <span>Top Customers by Spending</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data?.customer_stats?.top_customers_by_spending?.map((customer: {
                  id: number | string;
                  name: string;
                  average_order_value: number;
                  total_spending: number;
                }) => (
                  <div key={customer.id} className="flex items-center">
                    <div className="space-y-1">
                      <p className="text-sm font-medium leading-none">{customer.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Avg: {formatCurrency(customer.average_order_value)}
                      </p>
                    </div>
                    <div className="ml-auto font-medium">
                      {formatCurrency(customer.total_spending)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Financial Overview */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="border-b">
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" style={{ color: '#47b37c' }} />
                <span>Financial Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ShoppingBag className="h-4 w-4 opacity-70" />
                    <span className="text-sm">Gross Profit</span>
                  </div>
                  <span className="font-medium">{formatCurrency(data?.financial_overview?.gross_profit_last_30_days || 0)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 opacity-70" />
                    <span className="text-sm">Net Profit</span>
                  </div>
                  <span className="font-medium">{formatCurrency(data?.financial_overview?.net_profit_last_30_days || 0)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Activity className="h-4 w-4 opacity-70" />
                    <span className="text-sm">Profit Margin</span>
                  </div>
                  <span className="font-medium">{data?.financial_overview?.gross_profit_margin || 0}%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Customer Activity */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="border-b">
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" style={{ color: '#47b37c' }} />
                <span>Customer Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 opacity-70" />
                    <span className="text-sm">New Customers (30d)</span>
                  </div>
                  <span className="font-medium">{data?.customer_stats?.new_customers_last_30_days || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 opacity-70" />
                    <span className="text-sm">Growth Rate</span>
                  </div>
                  <span className="font-medium">{data?.customer_stats?.customer_growth_rate || 0}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 opacity-70" />
                    <span className="text-sm">Most Active</span>
                  </div>
                  <span className="font-medium">{data?.customer_stats?.top_customers_by_frequency?.[0]?.name || 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
            <p className="text-gray-500 text-sm">Get things done faster with one-click shortcuts</p>
          </div>
          <button className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
            View all <ArrowUpRight className="h-4 w-4" />
          </button>
        </div>
        {organizationId !== undefined && (
          <QuickActions organizationId={organizationId} />
        )}
      </div>
    </div>
  );
};

export default OrganizationDashboard;