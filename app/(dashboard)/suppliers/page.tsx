"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Row } from "@tanstack/react-table";
import { Filter, Plus, Trash, TruckIcon, UserIcon } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useEffect, useState } from "react";
import { FetchSuppliersRequest, FetchSuppliersResponse, Supplier } from "@/features/suppliers/types/suppliers";
import { useSupplierFormModal } from "@/features/suppliers/hooks/use-supplier-form-modal";
import { SupplierTable } from "@/features/suppliers/components/supplier-table";

export default function Page() {

  const [selectedRows, setSelectedRows] = useState<Row<Supplier>[]>([]);
  const [response, setResponse] = useState<FetchSuppliersResponse | null>(
    null
  );
  const supplierForm = useSupplierFormModal();

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<FetchSuppliersRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      query: search,
    }));
  }, [search]);
 
  return (
    <div className="space-y-6">
      <PageHeader 
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Suppliers Management" },
        ]}
        title="Suppliers Management"
        description="Manage your supply chain and supplier relationships"
        icon={TruckIcon}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UserIcon className="h-5 w-5" />
              Suppliers
            </CardTitle>
            <CardDescription className="text-sm">
              {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button
            onClick={() =>{
              supplierForm.open()
            }}
            size="sm"
            className="mt-4"
          >
            <Plus className="size-4 mr-2" />
            Add New
          </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
            <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                Filter
              </PopoverContent>
            </Popover>
          </div>
          <SupplierTable
            onRowSelectedChanged={setSelectedRows}
            onDataChange={(res) => res && setResponse(res)}
            filters={filters}
            organization_id={localStorage.getItem("current_organization") ?? ""}
          />
        </CardContent>
      </Card>
    </div>
  );
}