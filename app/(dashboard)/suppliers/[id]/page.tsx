"use client";

import React from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { decryptId } from "@/lib/utils";

export default function SupplierDetailPage() {
  const params = useParams();
  const router = useRouter();
  // Create a copy to avoid modifying the read-only params object
  const encryptedId = String(params.id || '');
  const supplierId = decryptId(encryptedId);

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Supplier Details</h1>
      <p>Supplier ID: {supplierId}</p>
      {/* TODO: Implement supplier detail view */}
    </div>
  );
}