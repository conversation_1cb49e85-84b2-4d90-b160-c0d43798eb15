"use client";

import { useState } from "react";
import { SettingsNavigation } from "@/features/settings/components/settings-navigation";
import { SettingsContent } from "@/features/settings/components/settings-content";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Settings } from "lucide-react";
import { PageHeader } from "@/components/page-header";

export type SettingsSection = 
  | "store-details"
  | "order-history" 
  | "transactions"
  | "notifications"
  | "security"
  | "billing"
  | "team-members"
  | "integrations";

const SettingsPage = () => {
  const [activeSection, setActiveSection] = useState<SettingsSection>("store-details");
  const [isMobileContentOpen, setIsMobileContentOpen] = useState(false);

  const handleSectionChange = (section: SettingsSection) => {
    setActiveSection(section);
    setIsMobileContentOpen(true);
  };

  const handleMobileBack = () => {
    setIsMobileContentOpen(false);
  };

  return (
    <>
      {/* Desktop Layout */}
      <div className="hidden lg:block bg-gray-50 -mx-6 -my-8 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="mb-8">
            <PageHeader
              title="Settings"
              description="Manage your store settings and preferences"
              icon={Settings}
            />
          </div>

          <div className="grid grid-cols-12 gap-8">
            {/* Navigation Sidebar */}
            <div className="col-span-3">
              <SettingsNavigation
                activeSection={activeSection}
                onSectionChange={handleSectionChange}
              />
            </div>

            {/* Content Area */}
            <div className="col-span-9">
              <SettingsContent activeSection={activeSection} />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout - Full Screen */}
      <div className="lg:hidden fixed inset-0 bg-gray-50 z-50">
        <div className="relative h-full w-full overflow-hidden">
          {/* Navigation Panel */}
          <div className={`absolute inset-0 w-full h-full transition-transform duration-300 ease-in-out ${
            isMobileContentOpen ? '-translate-x-full' : 'translate-x-0'
          }`}>
            <div className="h-full flex flex-col bg-gray-50">
              {/* Mobile Navigation Header */}
              <div className="bg-white border-b border-gray-200 px-6 py-6 shadow-sm">
                <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
                <p className="text-gray-600 mt-1">Manage your store settings</p>
              </div>

              {/* Navigation Content */}
              <div className="flex-1 p-4 overflow-y-auto">
                <SettingsNavigation
                  activeSection={activeSection}
                  onSectionChange={handleSectionChange}
                  isMobile={true}
                />
              </div>
            </div>
          </div>

          {/* Content Panel */}
          <div className={`absolute inset-0 w-full h-full transition-transform duration-300 ease-in-out ${
            isMobileContentOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
            <div className="h-full flex flex-col bg-gray-50">
              {/* Mobile Header with Back Button */}
              <div className="flex items-center gap-3 px-4 py-4 border-b border-gray-200 bg-white shadow-sm sticky top-0 z-10">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMobileBack}
                  className="p-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h2 className="text-lg font-semibold text-gray-900">Settings</h2>
              </div>

              {/* Mobile Content - Full Width */}
              <div className="flex-1 overflow-y-auto">
                <div className="p-4">
                  <SettingsContent activeSection={activeSection} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SettingsPage;
