"use client";

import { useParams } from "next/navigation";
import { decryptId } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { ArrowLeft, Calendar, CreditCard, DollarSign, FileText, Plus, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useRetrieveCredit } from "@/features/credits/api/use-retrieve-credits";
import { useCreditFormModal } from "@/features/credits/hooks/use-payment-form-modal";
import { CreditPaymentFormModal } from "@/features/credits/components/payments-form-modal";

import { formatDistanceToNow } from "date-fns";

export default function CreditDetailPage() {
    const params = useParams();
    const router = useRouter();
    // Create a copy to avoid modifying the read-only params object
    const encryptedId = String(params.id || '');
    const creditId = decryptId(encryptedId);

    const { data: credit, isLoading } = useRetrieveCredit(creditId);
    const { open } = useCreditFormModal();

    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={() => router.back()}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                </div>
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                    <div className="xl:col-span-2 space-y-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="animate-pulse space-y-4">
                                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                                    <div className="space-y-2">
                                        <div className="h-4 bg-gray-200 rounded"></div>
                                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        );
    }

    if (!credit?.data) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={() => router.back()}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                </div>
                <Card>
                    <CardContent className="p-6 text-center">
                        <p className="text-muted-foreground">Credit not found</p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    const creditData = credit.data;
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'paid':
                return 'bg-green-100 text-green-800';
            case 'partial':
                return 'bg-yellow-100 text-yellow-800';
            case 'pending':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatCurrency = (amount: number | string) => {
        return `TZS ${Number(amount).toLocaleString()}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={() => router.back()}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                    <div>
                        <h1 className="text-xl sm:text-2xl font-bold">Credit Details</h1>
                        <p className="text-muted-foreground text-sm sm:text-base">{creditData.reference}</p>
                    </div>
                </div>
                <Button onClick={() => open(creditId)} className="w-full sm:w-auto">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment
                </Button>
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                {/* Main Content */}
                <div className="xl:col-span-2 space-y-6">
                    {/* Credit Overview */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="h-5 w-5" />
                                Credit Overview
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                                    <Badge className={getStatusColor(creditData.status)}>
                                        {creditData.status.toUpperCase()}
                                    </Badge>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Date Created</p>
                                    <p className="font-medium">{formatDate(creditData.date)}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                                    <p className="text-lg font-bold">{formatCurrency(creditData.amount)}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Remaining Balance</p>
                                    <p className="text-lg font-bold text-destructive">{formatCurrency(creditData.balance)}</p>
                                </div>
                            </div>
                            {creditData.notes && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Notes</p>
                                    <p className="text-sm">{creditData.notes}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Payment History */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                Payment History
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {creditData.repayments && creditData.repayments.length > 0 ? (
                                <div className="space-y-4">
                                    {creditData.repayments.map((repayment: any) => (
                                        <div key={repayment.id} className="border rounded-lg p-3 sm:p-4">
                                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
                                                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                                                    <Badge variant="outline" className="w-fit">{repayment.reference}</Badge>
                                                    <span className="text-sm text-muted-foreground">
                                                        {formatDate(repayment.date)}
                                                    </span>
                                                </div>
                                                <div className="text-left sm:text-right">
                                                    <p className="font-semibold">{formatCurrency(repayment.amount)}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        Balance: {formatCurrency(repayment.balance)}
                                                    </p>
                                                </div>
                                            </div>
                                            {repayment.payment && (
                                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                                                    <div>
                                                        <span className="font-medium">Payment Method:</span>
                                                        <p className="text-muted-foreground capitalize">
                                                            {repayment.payment.method.replace('_', ' ')}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Status:</span>
                                                        <p className="text-muted-foreground capitalize">
                                                            {repayment.payment.status}
                                                        </p>
                                                    </div>
                                                </div>
                                            )}
                                            {repayment.notes && (
                                                <div className="mt-3">
                                                    <span className="text-sm font-medium">Notes:</span>
                                                    <p className="text-sm text-muted-foreground">{repayment.notes}</p>
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <DollarSign className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                    <p className="text-muted-foreground">No payments recorded yet</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6 xl:space-y-6">
                    {/* Customer Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Customer Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Name</p>
                                <p className="font-medium">{creditData.creditor?.name || 'N/A'}</p>
                            </div>
                            {creditData.creditor?.phone && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Phone</p>
                                    <p className="font-medium">{creditData.creditor.phone}</p>
                                </div>
                            )}
                            {creditData.creditor?.email && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                                    <p className="font-medium">{creditData.creditor.email}</p>
                                </div>
                            )}
                            {creditData.creditor?.address && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Address</p>
                                    <p className="font-medium">{creditData.creditor.address}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Related Sale Information */}
                    {creditData.creditable && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-5 w-5" />
                                    Related Sale
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Sale Code</p>
                                    <p className="font-medium">{creditData.creditable.code}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Sale Date</p>
                                    <p className="font-medium">{formatDate(creditData.creditable.date)}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                                    <p className="font-medium">{formatCurrency(creditData.creditable.total_amount)}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                                    <Badge variant="outline">{creditData.creditable.status}</Badge>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Quick Stats */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Quick Stats
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Payments</p>
                                <p className="font-medium">{creditData.repayments?.length || 0}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Amount Paid</p>
                                <p className="font-medium text-green-600">
                                    {formatCurrency(Number(creditData.amount) - Number(creditData.balance))}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Payment Progress</p>
                                <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div
                                        className="bg-green-600 h-2 rounded-full"
                                        style={{
                                            width: `${((Number(creditData.amount) - Number(creditData.balance)) / Number(creditData.amount)) * 100}%`
                                        }}
                                    ></div>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    {Math.round(((Number(creditData.amount) - Number(creditData.balance)) / Number(creditData.amount)) * 100)}% paid
                                </p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                                <p className="font-medium">
                                    {formatDistanceToNow(new Date(creditData.updated_at), { addSuffix: true })}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            <CreditPaymentFormModal />
        </div>
    );
}