"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import {  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lter, <PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";

import { Row } from "@tanstack/react-table";
import { Credits, FetchCreditRequest, FetchCreditResponse } from "@/features/credits/types/credits";
import { CreditDataTable } from "@/features/credits/components/credits-table";
import { PageHeader } from "@/components/page-header";
export default function Page() {
  
    const router = useRouter();
    const [selectedRows, setSelectedRows] = useState<Row<Credits>[]>([]);
    const [response, setResponse] = useState<FetchCreditResponse | null>(
        null
      );
    const [search, setSearch] = useState<string>("");
    const [filters, setFilters] = useState<FetchCreditRequest>({
      organization_id: localStorage.getItem("current_organization")!,
     });

  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      query: search,
    }));
  }, [search]);

 
  return (
    <div className="space-y-6">

      <PageHeader 
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Credit Management" },
        ]}
        title="Credit Management"
        description="Manage customer credit limits and payment terms"
        icon={CreditCardIcon}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="">Credit Management List</CardTitle>
            <CardDescription className="text-sm">
            {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button 
          variant="outline" 
          onClick={() => router.push('/credits/aging')}
        >
          <PieChart className="mr-2 h-4 w-4" />
          Aging Report
        </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
          <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                filter
              </PopoverContent>
            </Popover>
          </div>
            <CreditDataTable
            onRowSelectedChanged={setSelectedRows}
            onDataChange={(res) => res && setResponse(res)}
            filters={filters}
            organization_id={localStorage.getItem("current_organization")!}
             />
        </CardContent>
      </Card>
    </div>
  );
}