"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Row } from "@tanstack/react-table";
import { Filter, Plus, StoreIcon, Trash } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useEffect, useState } from "react";
import { useWareHouseFormModal } from "@/features/warehouses/hooks/use-warehouse-form-modal";
import { FetchWarehousesRequest, FetchWarehousesResponse, Warehouse } from "@/features/warehouses/types/warehouses";
import { WarehouseTable } from "@/features/warehouses/components/warehouse-table";

export default function Page() {

  const [selectedRows, setSelectedRows] = useState<Row<Warehouse>[]>([]);
  const [response, setResponse] = useState<FetchWarehousesResponse | null>(
    null
  );
  const wirehouseForm = useWareHouseFormModal();

  const [search, setSearch] = useState<string>("");
  const [filters, setFilters] = useState<FetchWarehousesRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      query: search,
    }));
  }, [search]);
 
  return (
    <div className="space-y-6">
      <PageHeader
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Warehouses Management" },
        ]}
        title="Warehouses Management"
        description="Manage wirehouses/Stores"
        icon={StoreIcon}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <StoreIcon className="h-5 w-5" />
              Warehouses
            </CardTitle>
            <CardDescription className="text-sm">
              {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button
            onClick={() =>{
              wirehouseForm.open()
            }}
            size="sm"
            className="mt-4"
          >
            <Plus className="size-4 mr-2" />
            Add New
          </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
            <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                Filter
              </PopoverContent>
            </Popover>
          </div>
          <WarehouseTable
            onRowSelectedChanged={setSelectedRows}
            onDataChange={(res) => res && setResponse(res)}
            filters={filters}
            organization_id={localStorage.getItem("current_organization") ?? ""}
          />
        </CardContent>
      </Card>
    </div>
  );
}