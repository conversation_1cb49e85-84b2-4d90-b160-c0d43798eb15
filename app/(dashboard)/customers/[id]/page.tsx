"use client";

import React from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { decryptId } from "@/lib/utils";

export default function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  // Create a copy to avoid modifying the read-only params object
  const encryptedId = String(params.id || '');
  const customerId = decryptId(encryptedId);

  return (
    <div className="space-y-6 p-6">
      <h1 className="text-2xl font-bold">Customer Details</h1>
      <p>Customer ID: {customerId}</p>
      {/* TODO: Implement customer detail view */}
    </div>
  );
}