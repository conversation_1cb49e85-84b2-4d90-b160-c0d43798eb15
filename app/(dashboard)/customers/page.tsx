"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Customers, FetchCustomerRequest, FetchCustomerResponse } from "@/features/customers/types/customers";
import {  Filter, Plus, Trash, UsersRound } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useEffect, useState } from "react";
import { CustomerDataTable } from "@/features/customers/components/customer-table";
import { Badge } from "@/components/ui/badge";
import { Row } from "@tanstack/react-table";
import { useCustomerFormModal } from "@/features/customers/hooks/use-customer-form-modal";
export default function Page() {

    const [selectedRows, setSelectedRows] = useState<Row<Customers>[]>([]);
    const [response, setResponse] = useState<FetchCustomerResponse | null>( null );
    const [search, setSearch] = useState<string>("");
    const [filters, setFilters] = useState<FetchCustomerRequest>({
        organization_id: localStorage.getItem("current_organization")!,
    });

    const customerFormModal = useCustomerFormModal();

  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      query: search,
    }));
  }, [search]);
 
  return (
    <div className="space-y-6">
      <PageHeader
        breadcrumbs={[
          { name: "Home", href: "/" },
          { name: "Dashboard" },
          { name: "Customer Management" },
        ]}
        title="Customer Management"
        description="Manage your customer relationships and information"
        icon={UsersRound}
      />

      <Card>
        <CardHeader className="gap-y-2 flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UsersRound className="h-5 w-5" />
              Customers List
            </CardTitle>
            <CardDescription className="text-sm">
            {response?.total ?? 0} in total
            </CardDescription>
          </div>
          <Button
            onClick={() =>{
              customerFormModal.open()
            }}
            size="sm"
            className="mt-4"
          >
            <Plus className="size-4 mr-2" />
            Add New
          </Button>
        </CardHeader>

        <CardContent>
          <div className="mb-2 flex justify-start items-stretch gap-x-2">
          <input
              className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <div className="flex-1"></div>

            <Button
              size="sm"
              variant="destructive"
              disabled={selectedRows.length == 0}
            >
              <Trash /> Bulk Delete{" "}
              {selectedRows.length > 0 && (
                <Badge variant="secondary">{selectedRows.length}</Badge>
              )}
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button size="sm" variant="secondary">
                  <Filter />
                </Button>
              </PopoverTrigger>

              <PopoverContent side="left" className="w-80">
                Filters
              </PopoverContent>
            </Popover>
          </div>
            <CustomerDataTable
                onRowSelectedChanged={setSelectedRows}
                onDataChange={(res) => res && setResponse(res)}
                filters={filters}
                organization_id={localStorage.getItem("current_organization")!}
             />
        </CardContent>
      </Card>
    </div>
  );
}