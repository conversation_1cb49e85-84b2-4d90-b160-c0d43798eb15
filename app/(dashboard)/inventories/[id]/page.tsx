"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useRetrieveInventory } from "@/features/inventories/api/use-retrieve-inventory";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Package, Barcode, Layers, DollarSign, History, Warehouse } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { formatCurrency, decryptId, encryptId } from "@/lib/utils";

export default function InventoryDetailPage() {
  const params = useParams();
  const router = useRouter();
  // Create a copy to avoid modifying the read-only params object
  const encryptedId = String(params.id || '');
  const inventoryId = decryptId(encryptedId);

  const { data: response, isLoading, error } = useRetrieveInventory(inventoryId);
  const inventory: any = response?.data;
  const stockItems: any[] = (response as any)?.instock ?? inventory?.stock_items ?? [];
  const stockTxns: any[] = (response as any)?.stock_transactions ?? [];

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2 space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !inventory) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">Inventory item not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Inventory Details</h1>
            <p className="text-muted-foreground text-sm sm:text-base">#{inventory.inventory_item_sku ?? inventory.id}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="xl:col-span-2 space-y-6">
          {/* Inventory Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" /> Item Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p className="font-medium">{inventory.name ?? '-'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">SKU</p>
                  <div className="flex items-center gap-2">
                    <Barcode className="h-4 w-4 text-muted-foreground" />
                    <p className="font-medium">{inventory.inventory_item_sku ?? '-'}</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Sale Price</p>
                  <p className="text-lg font-bold">{formatCurrency(Number(inventory.sale_price ?? 0))}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">In Stock</p>
                  <Badge className="bg-blue-100 text-blue-800">
                    {Number(inventory.in_stock ?? 0)}
                  </Badge>
                </div>
              </div>
              {inventory.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Description</p>
                  <p className="text-sm">{inventory.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Stock Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Warehouse className="h-5 w-5" /> Stock by Warehouse
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stockItems && stockItems.length > 0 ? (
                <div className="space-y-4">
                  {stockItems.map((s: any) => (
                    <div key={s.id} className="border rounded-lg p-3 sm:p-4">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div>
                          <div className="font-medium">Warehouse ID: {s.inv_warehouse_id ?? '-'}</div>
                          <div className="text-xs text-muted-foreground">
                            In Stock: {s.in_stock ?? s.quantity ?? 0} · Unit ID: {s.unit_id ?? '-'}
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="font-semibold">Unit Cost: {formatCurrency(Number(s.unit_cost ?? 0))}</p>
                          <p className="text-xs text-muted-foreground">Added: {formatDate(s.created_at)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Layers className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No stock recorded</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Stock Transactions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" /> Stock Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stockTxns && stockTxns.length > 0 ? (
                <div className="space-y-4">
                  {stockTxns.map((tx: any) => (
                    <div
                      key={tx.id}
                      className="border rounded-lg p-3 sm:p-4 cursor-pointer hover:bg-gray-50 transition"
                      onClick={() => {
                        const purchaseId = tx?.source?.purchase_id;
                        if (purchaseId) router.push(`/purchases/${encryptId(purchaseId)}`);
                      }}
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div>
                          <div className="font-medium">Qty: {tx.quantity ?? '-'}</div>
                          <div className="text-xs text-muted-foreground">
                            Source: {tx.source_type?.split('\\').pop() ?? '-'} #{tx.source_id} · Dest: {tx.destination_type?.split('\\').pop() ?? '-'} #{tx.destination_id}
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="font-semibold">Date: {formatDate(tx.created_at)}</p>
                          {tx?.source?.unit_price && (
                            <p className="text-xs text-muted-foreground">Unit Price: {formatCurrency(Number(tx.source.unit_price))}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No stock transactions recorded</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6 xl:space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="h-5 w-5" /> Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total In Stock</p>
                <p className="font-medium text-blue-600">{Number(inventory.in_stock ?? 0)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sale Price</p>
                <p className="font-medium text-green-600">{formatCurrency(Number(inventory.sale_price ?? 0))}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Default Unit ID</p>
                <p className="font-medium">{inventory.default_unit_id ?? '-'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p className="font-medium">
                  {inventory.updated_at ? formatDistanceToNow(new Date(inventory.updated_at), { addSuffix: true }) : '-'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}