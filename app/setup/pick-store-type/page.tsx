"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Store, 
  Pill, 
  ShoppingCart, 
  Coffee, 
  Shirt, 
  Wrench, 
  Book, 
  Car,
  ArrowLeft,
  Check
} from "lucide-react";
import { useRouter } from "next/navigation";

// Mock domain data - in real implementation, this would come from an API
const STORE_DOMAINS = [
  {
    id: "pharmacy",
    name: "Pharmacy",
    description: "Medical supplies, prescription drugs, and health products",
    icon: Pill,
    color: "bg-red-100 text-red-600 border-red-200",
    popular: true,
  },
  {
    id: "supermarket",
    name: "Supermarket",
    description: "Groceries, household items, and daily necessities",
    icon: ShoppingCart,
    color: "bg-green-100 text-green-600 border-green-200",
    popular: true,
  },
  {
    id: "restaurant",
    name: "Restaurant/Cafe",
    description: "Food service, beverages, and dining experiences",
    icon: Coffee,
    color: "bg-orange-100 text-orange-600 border-orange-200",
    popular: false,
  },
  {
    id: "clothing",
    name: "Clothing Store",
    description: "Fashion, apparel, and accessories",
    icon: Shirt,
    color: "bg-purple-100 text-purple-600 border-purple-200",
    popular: false,
  },
  {
    id: "hardware",
    name: "Hardware Store",
    description: "Tools, building materials, and home improvement",
    icon: Wrench,
    color: "bg-gray-100 text-gray-600 border-gray-200",
    popular: false,
  },
  {
    id: "bookstore",
    name: "Bookstore",
    description: "Books, stationery, and educational materials",
    icon: Book,
    color: "bg-blue-100 text-blue-600 border-blue-200",
    popular: false,
  },
  {
    id: "automotive",
    name: "Automotive",
    description: "Car parts, accessories, and automotive services",
    icon: Car,
    color: "bg-indigo-100 text-indigo-600 border-indigo-200",
    popular: false,
  },
  {
    id: "general",
    name: "General Store",
    description: "Mixed products and general merchandise",
    icon: Store,
    color: "bg-yellow-100 text-yellow-600 border-yellow-200",
    popular: false,
  },
];

export default function PickStoreTypePage() {
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);
  const router = useRouter();

  const handleDomainSelect = (domainId: string) => {
    setSelectedDomain(domainId);
  };

  const handleContinue = () => {
    if (selectedDomain) {
      // In real implementation, you would save the selected domain
      // and redirect to the dashboard or next setup step
      router.push("/setup/choose-organization");
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-primary rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <Store className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Pick Your Store Type
          </h1>
          <p className="text-gray-600 text-lg">
            Choose the domain that best describes your business
          </p>
        </div>

        {/* Main Card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-xl font-bold text-gray-900">
              Select Your Business Domain
            </CardTitle>
            <p className="text-gray-600 text-sm">
              This helps us customize the system for your specific needs
            </p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            {/* Domain Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              {STORE_DOMAINS.map((domain) => {
                const Icon = domain.icon;
                const isSelected = selectedDomain === domain.id;

                return (
                  <div
                    key={domain.id}
                    onClick={() => handleDomainSelect(domain.id)}
                    className={`
                      relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 
                      hover:shadow-md group
                      ${
                        isSelected
                          ? "border-primary bg-primary/5 shadow-md"
                          : "border-gray-200 hover:border-primary/50"
                      }
                    `}
                  >
                    {/* Popular Badge */}
                    {domain.popular && (
                      <Badge 
                        variant="secondary" 
                        className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs"
                      >
                        Popular
                      </Badge>
                    )}

                    {/* Selection Indicator */}
                    {isSelected && (
                      <div className="absolute top-4 right-4 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                    )}

                    {/* Icon */}
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${domain.color}`}>
                      <Icon className="w-6 h-6" />
                    </div>

                    {/* Content */}
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {domain.name}
                    </h3>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {domain.description}
                    </p>
                  </div>
                );
              })}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>

              <Button
                type="button"
                onClick={handleContinue}
                disabled={!selectedDomain}
                className="flex items-center gap-2 bg-primary hover:bg-primary/90"
              >
                Continue
                <Check className="w-4 h-4" />
              </Button>
            </div>

            {/* Help Text */}
            {selectedDomain && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>Great choice!</strong> We'll customize your dashboard and features 
                  specifically for {STORE_DOMAINS.find(d => d.id === selectedDomain)?.name.toLowerCase()} operations.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
