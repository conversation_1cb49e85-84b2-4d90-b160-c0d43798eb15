"use client";

import React, { useState, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Plus, 
  ShoppingCart, 
  Package,
  ArrowLeft,
  Pill,
  Coffee,
  Shirt,
  Wrench,
  Book,
  Car,
  Store
} from "lucide-react";
import { useRouter } from "next/navigation";
import { ProductCard } from "@/features/products/components/product-card";
import { AddProductModal } from "@/features/products/components/add-product-modal";
import { SelectedProductsModal } from "@/features/products/components/selected-products-modal";
import { toast } from "sonner";

// Mock product data
const MOCK_PRODUCTS = {
  pharmacy: [
    { id: "p1", name: "Paracetamol 500mg", description: "Pain relief tablets", category: "Medicine", image: "/api/placeholder/150/150" },
    { id: "p2", name: "Vitamin C", description: "Immune system support", category: "Supplements", image: "/api/placeholder/150/150" },
    { id: "p3", name: "Bandages", description: "First aid bandages", category: "Medical Supplies", image: "/api/placeholder/150/150" },
    { id: "p4", name: "Thermometer", description: "Digital thermometer", category: "Medical Equipment", image: "/api/placeholder/150/150" },
    { id: "p5", name: "Cough Syrup", description: "Cough relief medicine", category: "Medicine", image: "/api/placeholder/150/150" },
    { id: "p6", name: "Hand Sanitizer", description: "Antibacterial gel", category: "Hygiene", image: "/api/placeholder/150/150" },
  ],
  supermarket: [
    { id: "s1", name: "Rice 5kg", description: "Premium white rice", category: "Grains", image: "/api/placeholder/150/150" },
    { id: "s2", name: "Cooking Oil 1L", description: "Sunflower cooking oil", category: "Cooking", image: "/api/placeholder/150/150" },
    { id: "s3", name: "Bread", description: "Fresh white bread", category: "Bakery", image: "/api/placeholder/150/150" },
    { id: "s4", name: "Milk 1L", description: "Fresh dairy milk", category: "Dairy", image: "/api/placeholder/150/150" },
    { id: "s5", name: "Eggs (12 pcs)", description: "Fresh chicken eggs", category: "Dairy", image: "/api/placeholder/150/150" },
    { id: "s6", name: "Sugar 1kg", description: "White granulated sugar", category: "Pantry", image: "/api/placeholder/150/150" },
  ],
  restaurant: [
    { id: "r1", name: "Coffee Beans 1kg", description: "Premium arabica beans", category: "Beverages", image: "/api/placeholder/150/150" },
    { id: "r2", name: "Pasta 500g", description: "Italian spaghetti", category: "Food", image: "/api/placeholder/150/150" },
    { id: "r3", name: "Olive Oil", description: "Extra virgin olive oil", category: "Cooking", image: "/api/placeholder/150/150" },
    { id: "r4", name: "Tomato Sauce", description: "Fresh tomato sauce", category: "Condiments", image: "/api/placeholder/150/150" },
    { id: "r5", name: "Cheese", description: "Mozzarella cheese", category: "Dairy", image: "/api/placeholder/150/150" },
    { id: "r6", name: "Spices Set", description: "Mixed cooking spices", category: "Seasonings", image: "/api/placeholder/150/150" },
  ],
  clothing: [
    { id: "c1", name: "T-Shirt", description: "Cotton casual t-shirt", category: "Tops", image: "/api/placeholder/150/150" },
    { id: "c2", name: "Jeans", description: "Denim blue jeans", category: "Bottoms", image: "/api/placeholder/150/150" },
    { id: "c3", name: "Sneakers", description: "Comfortable sneakers", category: "Footwear", image: "/api/placeholder/150/150" },
    { id: "c4", name: "Jacket", description: "Winter jacket", category: "Outerwear", image: "/api/placeholder/150/150" },
    { id: "c5", name: "Dress", description: "Summer dress", category: "Dresses", image: "/api/placeholder/150/150" },
    { id: "c6", name: "Hat", description: "Baseball cap", category: "Accessories", image: "/api/placeholder/150/150" },
  ],
  hardware: [
    { id: "h1", name: "Hammer", description: "Steel claw hammer", category: "Tools", image: "/api/placeholder/150/150" },
    { id: "h2", name: "Screwdriver Set", description: "Multi-head screwdriver", category: "Tools", image: "/api/placeholder/150/150" },
    { id: "h3", name: "Paint Brush", description: "Professional paint brush", category: "Painting", image: "/api/placeholder/150/150" },
    { id: "h4", name: "Nails 1kg", description: "Steel construction nails", category: "Fasteners", image: "/api/placeholder/150/150" },
    { id: "h5", name: "Drill Bits", description: "HSS drill bit set", category: "Tools", image: "/api/placeholder/150/150" },
    { id: "h6", name: "Measuring Tape", description: "5m measuring tape", category: "Measuring", image: "/api/placeholder/150/150" },
  ],
  bookstore: [
    { id: "b1", name: "Notebook A4", description: "Ruled notebook", category: "Stationery", image: "/api/placeholder/150/150" },
    { id: "b2", name: "Pen Set", description: "Blue ink pens", category: "Writing", image: "/api/placeholder/150/150" },
    { id: "b3", name: "Calculator", description: "Scientific calculator", category: "Electronics", image: "/api/placeholder/150/150" },
    { id: "b4", name: "Textbook", description: "Mathematics textbook", category: "Books", image: "/api/placeholder/150/150" },
    { id: "b5", name: "Pencil Case", description: "Zipper pencil case", category: "Accessories", image: "/api/placeholder/150/150" },
    { id: "b6", name: "Ruler", description: "30cm plastic ruler", category: "Measuring", image: "/api/placeholder/150/150" },
  ],
  automotive: [
    { id: "a1", name: "Engine Oil", description: "5W-30 motor oil", category: "Fluids", image: "/api/placeholder/150/150" },
    { id: "a2", name: "Brake Pads", description: "Ceramic brake pads", category: "Brakes", image: "/api/placeholder/150/150" },
    { id: "a3", name: "Air Filter", description: "Engine air filter", category: "Filters", image: "/api/placeholder/150/150" },
    { id: "a4", name: "Spark Plugs", description: "Iridium spark plugs", category: "Engine", image: "/api/placeholder/150/150" },
    { id: "a5", name: "Car Battery", description: "12V car battery", category: "Electrical", image: "/api/placeholder/150/150" },
    { id: "a6", name: "Tire", description: "All-season tire", category: "Tires", image: "/api/placeholder/150/150" },
  ],
  general: [
    { id: "g1", name: "Batteries AA", description: "Alkaline batteries", category: "Electronics", image: "/api/placeholder/150/150" },
    { id: "g2", name: "Flashlight", description: "LED flashlight", category: "Tools", image: "/api/placeholder/150/150" },
    { id: "g3", name: "Plastic Bags", description: "Shopping bags", category: "Packaging", image: "/api/placeholder/150/150" },
    { id: "g4", name: "Cleaning Spray", description: "Multi-surface cleaner", category: "Cleaning", image: "/api/placeholder/150/150" },
    { id: "g5", name: "Toilet Paper", description: "Soft toilet tissue", category: "Hygiene", image: "/api/placeholder/150/150" },
    { id: "g6", name: "Candles", description: "Scented candles", category: "Home", image: "/api/placeholder/150/150" },
  ],
};

const DOMAIN_ICONS = {
  pharmacy: Pill,
  supermarket: ShoppingCart,
  restaurant: Coffee,
  clothing: Shirt,
  hardware: Wrench,
  bookstore: Book,
  automotive: Car,
  general: Store,
};

export default function SelectProductsPage() {
  const router = useRouter();
  const [selectedDomain, setSelectedDomain] = useState("pharmacy");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isSelectedModalOpen, setIsSelectedModalOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<any>(null);

  // Load selected domain from localStorage on mount
  React.useEffect(() => {
    const savedDomain = localStorage.getItem("selected_store_domain");
    if (savedDomain && MOCK_PRODUCTS[savedDomain as keyof typeof MOCK_PRODUCTS]) {
      setSelectedDomain(savedDomain);
    }
  }, []);

  // Filter products based on search query
  const filteredProducts = useMemo(() => {
    const products = MOCK_PRODUCTS[selectedDomain as keyof typeof MOCK_PRODUCTS] || [];
    if (!searchQuery) return products;
    
    return products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [selectedDomain, searchQuery]);

  const handleAddProduct = (product: any) => {
    setCurrentProduct(product);
    setIsAddModalOpen(true);
  };

  const handleProductAdded = (productData: any) => {
    setSelectedProducts(prev => [...prev, { ...currentProduct, ...productData }]);
    setIsAddModalOpen(false);
    setCurrentProduct(null);
    toast.success(`${currentProduct?.name} added to inventory!`);
  };

  const handleRemoveProduct = (productId: string) => {
    const product = selectedProducts.find(p => p.id === productId);
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
    if (product) {
      toast.info(`${product.name} removed from inventory`);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleFinish = () => {
    // In real implementation, submit selected products
    console.log("Selected products:", selectedProducts);
    toast.success(`Setup completed! ${selectedProducts.length} products added to your inventory.`);

    // Clear localStorage
    localStorage.removeItem("selected_store_domain");

    setTimeout(() => {
      router.push("/dashboard");
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Select Your Products</h1>
              <p className="text-gray-600">Choose products to add to your inventory</p>
            </div>
          </div>

          {/* Selected Products Counter */}
          <Button
            onClick={() => setIsSelectedModalOpen(true)}
            className="relative"
            disabled={selectedProducts.length === 0}
          >
            <Package className="h-4 w-4 mr-2" />
            Selected Products
            {selectedProducts.length > 0 && (
              <Badge className="ml-2 bg-red-500 text-white">
                {selectedProducts.length}
              </Badge>
            )}
          </Button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Domain Tabs */}
        <Tabs value={selectedDomain} onValueChange={setSelectedDomain} className="w-full">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 mb-6">
            {Object.keys(MOCK_PRODUCTS).map((domain) => {
              const Icon = DOMAIN_ICONS[domain as keyof typeof DOMAIN_ICONS];
              return (
                <TabsTrigger key={domain} value={domain} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline capitalize">{domain}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Products Grid */}
          {Object.keys(MOCK_PRODUCTS).map((domain) => (
            <TabsContent key={domain} value={domain}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onAdd={() => handleAddProduct(product)}
                    isSelected={selectedProducts.some(p => p.id === product.id)}
                  />
                ))}
              </div>
              
              {filteredProducts.length === 0 && (
                <div className="text-center py-12">
                  <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No products found</p>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Finish Button */}
        {selectedProducts.length > 0 && (
          <div className="fixed bottom-6 right-6">
            <Button onClick={handleFinish} size="lg" className="shadow-lg">
              Finish Setup
              <Package className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Modals */}
      <AddProductModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        product={currentProduct}
        onAdd={handleProductAdded}
      />

      <SelectedProductsModal
        isOpen={isSelectedModalOpen}
        onClose={() => setIsSelectedModalOpen(false)}
        products={selectedProducts}
        onRemove={handleRemoveProduct}
        onFinish={handleFinish}
      />
    </div>
  );
}
