// roles.ts
export enum Roles {
    SuperAdmin = "SuperAdmin",
    Admin = "Admin",
    Agent = "Agent",
    User = "User",
    Owner = "Owner",
    Manager = "Manager",
    Staff = "Staff",
    STORE_KEEPER = "STORE_KEEPER",
    

}

export function roleName(role: Roles): string {
    switch (role) {
        case Roles.Agent:
            return "AGENT";
        case Roles.User:
            return "USER";
        case Roles.Owner:
            return "OWNER";
        case Roles.Manager:
            return "MANAGER";
        case Roles.Staff:
            return "STAFF";
        case Roles.STORE_KEEPER:
            return "STORE_KEEPER";
       case Roles.Admin:
            return "ADMIN";
        case Roles.SuperAdmin:
            return "SUPER_ADMIN";
        default:
            return "";
    }
}