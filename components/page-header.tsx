import { <PERSON>ideIcon, ArrowLeft, <PERSON>evronR<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { motion, useMotionTemplate, useMotionValue } from "framer-motion";

interface PageHeaderProps {
  title: string;
  description?: string;
  icon: LucideIcon;
  className?: string;
  showBackButton?: boolean;
  gradientText?: boolean;
  breadcrumbs?: { name: string; href?: string }[];
  badge?: string;
  decorativeGlow?: boolean;
}

export const PageHeader = ({
  title,
  description,
  icon: Icon,
  className,
  showBackButton = true,
  gradientText = true,
  breadcrumbs,
  badge,
  decorativeGlow = true,
}: PageHeaderProps) => {
  const router = useRouter();
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  const handleBack = () => router.back();

  return (
    <motion.div 
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className={cn("relative", className)}
    >
      {/* Card container with elegant shadow and border */}
      <div 
        className="relative overflow-hidden rounded-xl border bg-card/50 backdrop-blur-sm shadow-sm group"
        onMouseMove={(e) => {
          const { left, top } = e.currentTarget.getBoundingClientRect();
          mouseX.set(e.clientX - left);
          mouseY.set(e.clientY - top);
        }}
      >
        {/* Card background layer */}
        <div className="absolute inset-0 bg-gradient-to-br from-card to-muted/50 -z-10" />
        
        {/* Dynamic gradient glow */}
        {decorativeGlow && (
          <motion.div
            className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-30 transition-opacity duration-500 pointer-events-none"
            style={{
              background: useMotionTemplate`radial-gradient(300px at ${mouseX}px ${mouseY}px, rgba(72, 179, 124, 0.15), transparent 80%)`,
            }}
          />
        )}

        {/* Inner padding */}
        <div className="p-6 sm:p-8 space-y-4">
          {/* Breadcrumbs */}
          {breadcrumbs && (
            <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
              {breadcrumbs.map((crumb, i) => (
                <motion.div 
                  key={i} 
                  className="flex items-center gap-1.5"
                  whileHover={{ x: i === breadcrumbs.length - 1 ? 0 : 2 }}
                >
                  {i > 0 && <ChevronRight className="h-4 w-4" />}
                  {crumb.href ? (
                    <a
                      href={crumb.href}
                      className="hover:text-[#47b37c] transition-colors hover:underline underline-offset-4"
                    >
                      {crumb.name}
                    </a>
                  ) : (
                    <span className="text-foreground/80">{crumb.name}</span>
                  )}
                </motion.div>
              ))}
            </div>
          )}

          {/* Main content */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div className="flex-1 min-w-0 space-y-3">
              <div className="flex items-center gap-3">
                {/* Icon with floating effect */}
                <motion.div 
                  whileHover={{ scale: 1.05, rotate: 2 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative p-2.5 rounded-lg bg-[#c8ffd9]/20 backdrop-blur-sm border border-[#47b37c]/20 shadow-sm"
                >
                  <Icon className="h-6 w-6 text-[#47b37c]" />
                  <motion.div 
                    className="absolute inset-0 rounded-lg bg-[#47b37c]/10 opacity-0 group-hover:opacity-100 -z-10"
                    transition={{ duration: 0.3 }}
                  />
                </motion.div>

                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className={cn(
                    "text-2xl sm:text-3xl font-bold tracking-tight relative",
                    gradientText && "bg-gradient-to-r from-[#47b37c] to-[#133330] bg-clip-text text-transparent"
                  )}>
                    {title}
                    {/* Subtle sparkle decoration */}
                    <motion.span 
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3 }}
                      className="absolute -right-5 -top-1 text-[#47b37c]"
                    >
                      <Sparkles className="h-4 w-4" />
                    </motion.span>
                  </h1>
                  
                  {badge && (
                    <motion.span 
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="px-2.5 py-1 text-xs font-medium rounded-full bg-[#47b37c]/10 text-[#133330] border border-[#47b37c]/20 flex items-center gap-1.5"
                    >
                      <span className="relative flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-[#47b37c] opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-[#47b37c]"></span>
                      </span>
                      {badge}
                    </motion.span>
                  )}
                </div>
              </div>

              {description && (
                <motion.p 
                  initial={{ opacity: 0.8, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="text-muted-foreground max-w-3xl leading-relaxed relative pl-2 before:absolute before:left-0 before:top-1 before:bottom-1 before:w-0.5 before:bg-[#47b37c]/30 before:rounded-full"
                >
                  {description}
                </motion.p>
              )}
            </div>

            {showBackButton && (
              <motion.div
                whileHover={{ x: -3 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBack}
                  className="gap-2 flex-shrink-0 border-[#47b37c]/20 hover:bg-[#c8ffd9]/10 hover:border-[#47b37c]/30 transition-all shadow-sm"
                >
                  <ArrowLeft className="h-4 w-4 text-[#47b37c]" />
                  <span className="text-[#133330]">Back</span>
                </Button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Animated bottom border */}
        <motion.div 
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative h-px overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#47b37c]/30 to-transparent animate-[shimmer_2s_infinite]"></div>
        </motion.div>
      </div>
    </motion.div>
  );
};