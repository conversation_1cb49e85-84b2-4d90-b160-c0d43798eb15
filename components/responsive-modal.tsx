import React from "react";
import { useMedia } from "react-use";
import { Dialog, DialogContent } from "./ui/dialog";
import { Drawer, DrawerContent } from "./ui/drawer";
import { DialogTitle } from "@radix-ui/react-dialog";

interface ResponsiveModalProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isNormal?: boolean;
  customWidth?: string;
}

export const ResponsiveModal = ({
  children,
  open,
  onOpenChange,
  isNormal = true,
  customWidth,
}: ResponsiveModalProps) => {
  const media = useMedia("(min-width: 1024px)", false);

  const getWidthClass = () => {
    if (customWidth) return customWidth;
    return isNormal ? 'md:max-w-lg' : 'md:max-w-5xl';
  };

  if (media) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
            className={`w-full sm:max-w-lg ${getWidthClass()} p-0 border-none overflow-y-auto hide-scrollbar max-h-[85vh] transition-all duration-300 ease-in-out`}>
          <DialogTitle></DialogTitle>
          {children}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <div className="overflow-y-auto hide-scrollbar max-h-[85vh]">
          {children}
        </div>
      </DrawerContent>
    </Drawer>
  );
};