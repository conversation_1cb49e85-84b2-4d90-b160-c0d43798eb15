import { CreditPaymentFormModal } from "@/features/credits/components/payments-form-modal";
import { CustomerFormModal } from "@/features/customers/components/customer-form-modal";
import { ExpenseCategoryFormModal } from "@/features/expense_category/components/expense-category-form-modal";
import { ExpenseFormModal } from "@/features/expenses/components/expense-form-modal";
import { SupplierFormModal } from "@/features/suppliers/components/supplier-form-modal";
import { StoreUserFormModal } from "@/features/users/components/user-form-modal";
import { TokenPurchaseModal } from "@/features/tokens/components/token-purchase-modal";
import { PurchaseFormModal } from "@/features/purchases/components/purchase-form-modal";
import { WarehouseFormModal } from "@/features/warehouses/components/warehouse-form-modal";
import { InventoryImportFormModal } from "@/features/inventories/components/inventory-import-form-modal";
import { InventoryCategoryFormModal } from "@/features/inventory_category/components/inventory-category-form-modal";
import { SaleFormModal } from "@/features/sales/components/sales-form-modal";

export default function IziFormModals() {
    return (
        <>
            <CustomerFormModal />
            <ExpenseCategoryFormModal />
            <ExpenseFormModal />
            <StoreUserFormModal />
            <SupplierFormModal />
            <CreditPaymentFormModal />
            <TokenPurchaseModal />
            <PurchaseFormModal />
            <WarehouseFormModal />
            <InventoryImportFormModal />
            <InventoryCategoryFormModal />
            <SaleFormModal />
        </>
    );
}