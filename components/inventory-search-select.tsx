import React, { useState, useEffect } from 'react';
import { InventoryItem } from '@/features/inventories/types/inventories';
import { 
  Select, 
  SelectContent, 
  SelectGroup, 
  SelectItem, 
  SelectLabel, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

interface InventorySearchSelectProps {
  inventories?: { data?: InventoryItem[] };
  value: string;
  onChange: (value: string) => void;
  fallbackInventoryName?: string;
}

const InventorySearchSelect = ({ 
  inventories, 
  value, 
  onChange, 
  fallbackInventoryName 
}: InventorySearchSelectProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredInventories, setFilteredInventories] = useState<InventoryItem[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (inventories?.data) {
      if (searchTerm.trim() === '') {
        setFilteredInventories(inventories.data);
      } else {
        const filtered = inventories.data.filter(inv => 
          typeof inv.name === 'string' && inv.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredInventories(filtered);
      }
    }
  }, [searchTerm, inventories]);

  return (
    <div className="relative w-full">
      <Select
        value={value}
        onValueChange={(newValue) => {
          onChange(newValue);
          setIsOpen(false);
        }}
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <SelectTrigger 
          className="w-full border-[#47b37c] focus:ring-[#47b37c]"
          onClick={() => setIsOpen(true)}
        >
          <SelectValue placeholder="Select Inventory" />
        </SelectTrigger>
        <SelectContent 
          className="bg-white border-[#47b37c] max-h-80"
          position="popper"
        >
          <div className="p-2 sticky top-0 bg-white z-10 border-b border-[#c8ffd9]">
            <Input
              placeholder="Search inventories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border-[#47b37c] focus:ring-[#47b37c]"
              autoFocus
            />
          </div>
          <SelectGroup className="overflow-y-auto max-h-60">
            <SelectLabel className="text-[#133330]">Inventories</SelectLabel>
            {/* Fallback option */}
            {!inventories?.data?.find(inv => inv.id.toString() === value) && 
             value && fallbackInventoryName && (
              <SelectItem 
                key={`fallback-inv-${value}`} 
                value={value}
                className="hover:bg-[#c8ffd9] focus:bg-[#c8ffd9]"
              >
                {fallbackInventoryName}
              </SelectItem>
            )}
            {filteredInventories.map((inventory) => (
              <SelectItem 
                key={inventory.id} 
                value={inventory.id.toString()}
                className="hover:bg-[#c8ffd9] focus:bg-[#c8ffd9]"
              >
                {inventory.name}
              </SelectItem>
            ))}
            {filteredInventories.length === 0 && searchTerm.trim() !== '' && (
              <div className="p-2 text-gray-500 text-center">
                No matching inventories found
              </div>
            )}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default InventorySearchSelect;