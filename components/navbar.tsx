import { UserButton } from "@/features/auth/components/user-button";
import { MobileSidebar } from "./mobile-sidebar";
import { OrganizationSelector } from "./organization-selector";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";
import { Coins, ChevronDown, LayoutDashboard, Bell, HelpCircle, CreditCard } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Organization } from "@/features/organizations/types/organization";
import { useTokenPurchaseModal } from "@/features/tokens/hooks/use-token-purchase-modal";

export const Navbar = () => {
    const { currentOrganization } = useStateManager();
    const pathname = usePathname();
    const tokenModal = useTokenPurchaseModal();

    // Parse organizations from localStorage
    const organizationsRaw = localStorage.getItem("organizations");
    let organizations;
    try {
        organizations = organizationsRaw ? JSON.parse(organizationsRaw) : undefined;
    } catch {
        organizations = undefined;
    }

    return (
        <nav className="sticky top-0 z-50 w-full border-b border-gray-100 bg-white/80 backdrop-blur-lg py-3 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
                {/* Left Section */}
                <div className="flex items-center gap-4">
                    <MobileSidebar />
                    
                    <div className="hidden lg:flex items-center gap-2">
                        <LayoutDashboard className="h-5 w-5 text-indigo-600" />
                        <div className="flex flex-col">
                            <h1 className="text-xl font-bold text-gray-900">Business Dashboard</h1>
                            <p className="text-xs text-gray-500 flex items-center gap-1">
                                <span className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></span>
                                <span>Live updates</span>
                            </p>
                        </div>
                    </div>
                </div>

                {/* Right Section */}
                <div className="flex items-center gap-4">
                    {/* Help Center */}
                    <button className="hidden md:flex items-center justify-center p-2 rounded-full hover:bg-gray-100 transition-colors">
                        <HelpCircle className="h-5 w-5 text-gray-500" />
                    </button>
                    
                    {/* Notifications */}
                    <button className="hidden md:flex items-center justify-center p-2 rounded-full hover:bg-gray-100 transition-colors relative">
                        <Bell className="h-5 w-5 text-gray-500" />
                        <span className="absolute top-1 right-1 w-2 h-2 rounded-full bg-red-500"></span>
                    </button>

                    {/* Organization Selector */}
                    <OrganizationSelector
                        currentOrganization={currentOrganization || undefined}
                        organizations={organizations}
                    />

                    {/* Token Display */}
                    {pathname !== "/" && currentOrganization && (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <button className="hidden lg:flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-amber-50 to-amber-100 border border-amber-200 rounded-lg hover:shadow-sm transition-all cursor-pointer">
                                    <div className="p-1.5 bg-amber-100 rounded-md">
                                        <Coins className="h-4 w-4 text-amber-600" />
                                    </div>
                                    <div className="flex flex-col">
                                        <span className="text-sm font-semibold text-amber-800">
                                            {(currentOrganization as Organization).tokens?.toLocaleString() || 0}
                                        </span>
                                        <span className="text-xs text-amber-600">Available tokens</span>
                                    </div>
                                    <ChevronDown className="h-4 w-4 text-amber-500" />
                                </button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel className="flex items-center gap-2">
                                    <Coins className="h-4 w-4 text-amber-600" />
                                    Token Management
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem asChild>
                                    <Button
                                        variant="ghost"
                                        className="w-full justify-start h-auto p-2 font-normal"
                                        onClick={() => {
                                            tokenModal.open();
                                        }}
                                    >
                                        <CreditCard className="h-4 w-4 mr-2 text-green-600" />
                                        <div className="flex flex-col items-start">
                                            <span className="font-medium">Buy Tokens</span>
                                            <span className="text-xs text-muted-foreground">Purchase more tokens</span>
                                        </div>
                                    </Button>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}

                    {/* User Button */}
                    <div className="flex items-center gap-2">
                        <UserButton />
                        <ChevronDown className="h-4 w-4 text-gray-400 hidden lg:block" />
                    </div>
                </div>
            </div>
        </nav>
    );
}