"use client";
import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getFilteredRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  Row,
  PaginationState,
  OnChangeFn,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "./ui/button";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  manualPagination?: boolean;
  manualSorting?: boolean;
  total?: number;
  showHeaders?: boolean;
  showPagination?: boolean;
  onPaginationChange?: (state: PaginationState) => void;
  onRowSelectedChanged?: (rows: Row<TData>[]) => void;
  onSortingChange?: (state: SortingState) => void;
  onUpdateData?: (rowIndex: number, columnId: string, value: string) => void;
  sorting?: SortingState;
  isLoading?: boolean;
  meta?: Record<string, unknown>;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  manualPagination = false,
  manualSorting = false,
  total,
  showHeaders = true,
  showPagination = true,
  onPaginationChange,
  onRowSelectedChanged,
  onSortingChange,
  onUpdateData,
  sorting: externalSorting,
  isLoading = false,
  meta,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>(externalSorting || []);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = React.useState({});
  const [pagination, setPagination] = React.useState({
    pageIndex: 0, //initial page index
    pageSize: 10, //default page size
  });

  // Update internal sorting state when external sorting changes
  React.useEffect(() => {
    if (externalSorting) {
      setSorting(externalSorting);
    }
  }, [externalSorting]);

  React.useEffect(() => {
    onPaginationChange?.(pagination);
  }, [pagination, onPaginationChange]);

  // We need to properly handle the OnChangeFn type from TanStack Table
  const handleSortingChange: OnChangeFn<SortingState> = React.useCallback(
    (updaterOrValue) => {
      setSorting(updaterOrValue);
      if (onSortingChange) {
        // If it's a function, we need to get the new value by applying it to the current state
        const newValue = typeof updaterOrValue === 'function' 
          ? updaterOrValue(sorting) 
          : updaterOrValue;
        onSortingChange(newValue);
      }
    },
    [onSortingChange, sorting]
  );

  const table = useReactTable<TData>({
    data,
    columns,
    manualPagination,
    manualSorting,
    rowCount: total ?? data.length ?? 0,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: handleSortingChange,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      rowSelection,
      pagination,
    },
    meta: {
      updateData: onUpdateData ?? null,
      ...meta
    },
  });

  React.useEffect(() => {
    onRowSelectedChanged?.(table.getFilteredSelectedRowModel().rows);
  }, [rowSelection, onRowSelectedChanged, table]);

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          {showHeaders && (
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
          )}
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center h-full">
                    <svg className="animate-spin h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {showPagination && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}