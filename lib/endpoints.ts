
export default class IziApi {

    public static secure = true;

    // public static api = process.env.NEXT_PUBLIC_LOCAL_API_URL;
    public static api = process.env.NEXT_PUBLIC_LIVE_API_URL;

    public static domain ="mangipro.comptech.co.tz";
    // public static domain = "127.0.0.1:8000";
    public static baseUrl = this.secure ? `https://${this.domain}` : `http://${this.domain}`;
    public static getImagesUrl = (path: string) => `${this.baseUrl}${path}`;

    // Auth endpoints
    public static login = `${this.api}/auth/login`;
    public static register = `${this.api}/auth/register`;
    public static loggedInUser = `${this.api}/auth/user`;

    // organizations endpoints
    public static organizations = `${this.api}/organizations`;
    
    public static getOrganizations = `${this.api}/config/organizations`;
    public static singleOrganization = (id: number) => `${this.api}/config/organizations/${id}`;


    // customers endpoints
    public static customers = `${this.api}/customer/customers`;

    // users endpoints
    public static users = `${this.api}/config/users`;

    //sales endpoints
    public static sales = `${this.api}/sale/sales`;

    //inventories endpoints
    public static inventories = `${this.api}/inventory/inventoryItems`;

    //inventory categories endpoints
    public static inventoryCategories = `${this.api}/inventory/inventoryCategories`;
    
    //purchases endpoints
    public static purchases = `${this.api}/purchase/purchases`;

    //expenses endpoints
    public static expenses = `${this.api}/expenses`;

    //expense categories endpoints
    public static expenseCategory = `${this.api}/expense-categories`;

    // suppliers endpoints
    public static suppliers = `${this.api}/supplies`;

    //credits endpoints
    public static credits = `${this.api}/credits`;

    //warehouses endpoints
    public static warehouses = `${this.api}/inventory/inventoryWarehouses`;

    //expenses reports endpoints
    public static expensesReport = `${this.api}/report/expenses`;

    public static buniseeReports = `${this.api}/organizations/summary-trend`;

    // dashboard stats endpoint
    public static dashboardStats = (id:number)=>`${this.api}/organizations/${id}/stats`;

    // inventory stock report endpoint
    public static inventoryStockReport = `${this.api}/report/inventory/stock`;

    //product performance report endpoint
    public static productPerformanceReport = `${this.api}/report/product`;

    //credit report endpoint 
    public static creditReport = `${this.api}/credits/reports/aging`;

    //sales report endpoint
    public static salesReport = `${this.api}/report/sales`;



}