import * as XLSX from 'xlsx';

export interface ExcelColumn {
  key: string;
  header: string;
  formatter?: (value: any, row: any) => string | number;
}

export interface ExcelExportOptions {
  filename?: string;
  sheetName?: string;
  columns: ExcelColumn[];
  data: any[];
}

/**
 * Reusable utility function to export data to Excel
 * @param options - Configuration options for the export
 */
export const exportToExcel = (options: ExcelExportOptions) => {
  const {
    filename = 'export',
    sheetName = 'Sheet1',
    columns,
    data
  } = options;

  // Transform data according to column configuration
  const transformedData = data.map(row => {
    const transformedRow: any = {};
    
    columns.forEach(column => {
      const value = getNestedValue(row, column.key);
      transformedRow[column.header] = column.formatter 
        ? column.formatter(value, row)
        : value;
    });
    
    return transformedRow;
  });

  // Create workbook and worksheet
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(transformedData);

  // Auto-size columns
  const columnWidths = columns.map(column => ({
    wch: Math.max(column.header.length, 15) // Minimum width of 15 characters
  }));
  worksheet['!cols'] = columnWidths;

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // Generate filename with timestamp
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const finalFilename = `${filename}_${timestamp}.xlsx`;

  // Write and download file
  XLSX.writeFile(workbook, finalFilename);
};

/**
 * Helper function to get nested object values using dot notation
 * @param obj - The object to extract value from
 * @param path - The path to the value (e.g., 'user.profile.name')
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : '';
  }, obj);
};

/**
 * Format currency for Excel export
 */
export const formatCurrencyForExcel = (value: any): string => {
  const num = Number(value);
  return isNaN(num) ? '0' : num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

/**
 * Format date for Excel export
 */
export const formatDateForExcel = (dateString: string): string => {
  if (!dateString) return '';
  try {
    return new Date(dateString).toLocaleDateString('en-US');
  } catch {
    return dateString;
  }
};

/**
 * Format boolean for Excel export
 */
export const formatBooleanForExcel = (value: any): string => {
  return value ? 'Yes' : 'No';
};


